'use client'

import { useEffect, useState } from 'react'
import { Search, Filter, AlertTriangle, Eye, CheckCircle, XCircle, Clock, Flag, User, MessageCircle } from 'lucide-react'

interface Report {
  id: string
  reporterId: string
  reporterEmail: string
  reportedUserId: string
  reportedUserEmail: string
  reportedUserName?: string
  reason: string
  description: string
  status: string // Can be 'pending', 'reviewed', 'resolved', 'dismissed' (case-insensitive)
  createdAt: string
  updatedAt: string
  adminNotes?: string
}

export default function AdminReports() {
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [showModal, setShowModal] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [confirmAction, setConfirmAction] = useState<{action: string, reportId: string} | null>(null)
  const [selectedReports, setSelectedReports] = useState<Set<string>>(new Set())
  const [showBulkActions, setShowBulkActions] = useState(false)

  useEffect(() => {
    fetchReports()
  }, [currentPage, searchTerm, filterStatus, filterType])

  const fetchReports = async () => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        status: filterStatus,
        type: filterType
      })

      const response = await fetch(`/api/admin/reports?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setReports(data.reports || [])
        setTotalPages(data.totalPages || 1)
      }
    } catch (error) {
      console.error('Failed to fetch reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReportAction = async (reportId: string, action: string, notes?: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch(`/api/admin/reports/${reportId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminNotes: notes })
      })

      if (response.ok) {
        fetchReports() // Refresh the list
        setShowModal(false)
        setSelectedReport(null)
        setShowConfirmDialog(false)
        setConfirmAction(null)
      }
    } catch (error) {
      console.error(`Failed to ${action} report:`, error)
    }
  }

  const confirmReportAction = (action: string, reportId: string) => {
    setConfirmAction({ action, reportId })
    setShowConfirmDialog(true)
  }

  const executeConfirmedAction = () => {
    if (confirmAction) {
      handleReportAction(confirmAction.reportId, confirmAction.action)
    }
  }

  const toggleReportSelection = (reportId: string) => {
    const newSelected = new Set(selectedReports)
    if (newSelected.has(reportId)) {
      newSelected.delete(reportId)
    } else {
      newSelected.add(reportId)
    }
    setSelectedReports(newSelected)
    setShowBulkActions(newSelected.size > 0)
  }

  const selectAllReports = () => {
    const allReportIds = new Set(reports.map(r => r.id))
    setSelectedReports(allReportIds)
    setShowBulkActions(true)
  }

  const clearSelection = () => {
    setSelectedReports(new Set())
    setShowBulkActions(false)
  }

  const handleBulkAction = async (action: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const promises = Array.from(selectedReports).map(reportId =>
        fetch(`/api/admin/reports/${reportId}/${action}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      )

      await Promise.all(promises)
      fetchReports()
      clearSelection()
    } catch (error) {
      console.error(`Failed to ${action} reports:`, error)
    }
  }

  const getStatusIcon = (status: string) => {
    const upperStatus = status.toUpperCase()
    switch (upperStatus) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'REVIEWED':
        return <Eye className="h-4 w-4 text-blue-500" />
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'DISMISSED':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-3 py-1.5 text-xs font-semibold rounded-full border"
    const upperStatus = status.toUpperCase()
    switch (upperStatus) {
      case 'PENDING':
        return <span className={`${baseClasses} bg-amber-50 text-amber-800 border-amber-200`}>Pending</span>
      case 'REVIEWED':
        return <span className={`${baseClasses} bg-blue-50 text-blue-800 border-blue-200`}>Reviewed</span>
      case 'RESOLVED':
        return <span className={`${baseClasses} bg-emerald-50 text-emerald-800 border-emerald-200`}>Resolved</span>
      case 'DISMISSED':
        return <span className={`${baseClasses} bg-slate-50 text-slate-700 border-slate-200`}>Dismissed</span>
      default:
        return <span className={`${baseClasses} bg-slate-50 text-slate-700 border-slate-200`}>Unknown</span>
    }
  }

  const getTypeIcon = (reason: string) => {
    if (!reason) return <Flag className="h-4 w-4 text-gray-400" />
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    }
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return <MessageCircle className="h-4 w-4 text-red-600" />
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return <User className="h-4 w-4 text-orange-500" />
    }
    if (upperReason.includes('SPAM')) {
      return <Flag className="h-4 w-4 text-purple-500" />
    }
    return <Flag className="h-4 w-4 text-gray-400" />
  }

  const formatReportType = (reason: string) => {
    if (!reason) return 'Unknown'
    return reason.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getPriorityColor = (reason: string) => {
    if (!reason) return 'border-l-gray-300 bg-white'
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return 'border-l-red-500 bg-red-50'
    }
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return 'border-l-orange-500 bg-orange-50'
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return 'border-l-yellow-500 bg-yellow-50'
    }
    return 'border-l-gray-300 bg-white'
  }

  const getPriorityBorderColor = (reason: string) => {
    if (!reason) return 'bg-gray-300'
    const upperReason = reason.toUpperCase()
    if (upperReason.includes('HARASSMENT') || upperReason.includes('ABUSE')) {
      return 'bg-red-500'
    }
    if (upperReason.includes('INAPPROPRIATE') || upperReason.includes('CONTENT')) {
      return 'bg-orange-500'
    }
    if (upperReason.includes('FAKE') || upperReason.includes('PROFILE')) {
      return 'bg-yellow-500'
    }
    return 'bg-gray-300'
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">User Reports</h1>
            <p className="text-slate-600 mt-2">Review and manage user safety reports</p>
          </div>
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 px-6 py-4">
            <div className="h-4 bg-slate-200 rounded w-24 animate-pulse mb-2"></div>
            <div className="h-8 bg-slate-200 rounded w-12 animate-pulse"></div>
          </div>
        </div>

        {/* Loading Search Bar */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 h-12 bg-slate-200 rounded-lg animate-pulse"></div>
            <div className="w-32 h-12 bg-slate-200 rounded-lg animate-pulse"></div>
            <div className="w-32 h-12 bg-slate-200 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Loading Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden animate-pulse">
              <div className="h-1.5 bg-slate-200"></div>
              <div className="p-6 space-y-5">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-slate-200 rounded"></div>
                    <div className="h-4 bg-slate-200 rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-slate-200 rounded-full w-20"></div>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                </div>
                <div className="h-20 bg-slate-200 rounded-lg"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-slate-200 rounded w-1/3"></div>
                  <div className="h-3 bg-slate-200 rounded w-2/3"></div>
                </div>
                <div className="flex gap-3">
                  <div className="flex-1 h-10 bg-slate-200 rounded-lg"></div>
                  <div className="flex-1 h-10 bg-slate-200 rounded-lg"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">User Reports</h1>
          <p className="text-slate-600 mt-2">Review and manage user safety reports</p>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 px-6 py-4">
          <p className="text-sm font-medium text-slate-600">Pending Reports</p>
          <p className="text-3xl font-bold text-red-600 mt-1">
            {reports.filter(r => r.status.toUpperCase() === 'PENDING').length}
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
            <input
              type="text"
              placeholder="Search by reporter or reported user email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-11 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-slate-900 placeholder-slate-500 bg-white"
            />
          </div>
          <div className="flex items-center gap-3">
            <Filter className="h-5 w-5 text-slate-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-slate-900 bg-white min-w-[120px]"
            >
              <option value="all">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="REVIEWED">Reviewed</option>
              <option value="RESOLVED">Resolved</option>
              <option value="DISMISSED">Dismissed</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-slate-900 bg-white min-w-[140px]"
            >
              <option value="all">All Types</option>
              <option value="HARASSMENT">Harassment</option>
              <option value="INAPPROPRIATE_CONTENT">Inappropriate Content</option>
              <option value="FAKE_PROFILE">Fake Profile</option>
              <option value="SPAM">Spam</option>
              <option value="OTHER">Other</option>
            </select>
          </div>

          {/* Select All Button */}
          <div className="flex items-center gap-2">
            <button
              onClick={selectedReports.size === reports.length ? clearSelection : selectAllReports}
              className="px-4 py-2 text-sm bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors font-medium border border-slate-200"
            >
              {selectedReports.size === reports.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedReports.size} report{selectedReports.size !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={clearSelection}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => handleBulkAction('dismiss')}
                className="px-4 py-2 text-sm bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors font-medium"
              >
                Dismiss Selected
              </button>
              <button
                onClick={() => handleBulkAction('review')}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Mark as Reviewed
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reports Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {reports.length === 0 ? (
          <div className="col-span-full bg-white rounded-xl shadow-sm border border-slate-200 p-16 text-center">
            <Flag className="h-20 w-20 mx-auto mb-6 text-slate-300" />
            <p className="text-xl font-semibold text-slate-600 mb-3">No reports found</p>
            <p className="text-sm text-slate-500">Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          reports.map((report) => (
            <div
              key={report.id}
              className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg hover:border-slate-300 transition-all duration-300 overflow-hidden"
            >
              {/* Card Header */}
              <div className={`h-1.5 ${getPriorityBorderColor(report.reason)}`}></div>

              <div className="p-6">
                {/* Checkbox and Status */}
                <div className="flex items-center justify-between mb-5">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={selectedReports.has(report.id)}
                      onChange={() => toggleReportSelection(report.id)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    {getTypeIcon(report.reason)}
                    <span className="text-sm font-semibold text-slate-800">
                      {formatReportType(report.reason)}
                    </span>
                  </div>
                  {getStatusBadge(report.status)}
                </div>

                {/* Reported User */}
                <div className="mb-5">
                  <div className="flex items-center gap-2 mb-3">
                    <User className="h-4 w-4 text-slate-500" />
                    <span className="text-sm font-semibold text-slate-700">Reported User</span>
                  </div>
                  <p className="font-bold text-slate-900 truncate text-base">
                    {report.reportedUserName || 'No name'}
                  </p>
                  <p className="text-sm text-slate-600 truncate mt-1">{report.reportedUserEmail}</p>
                </div>

                {/* Description */}
                <div className="mb-5">
                  <p className="text-sm font-semibold text-slate-700 mb-3">Description</p>
                  <p className="text-sm text-slate-800 line-clamp-3 bg-slate-50 p-4 rounded-lg border border-slate-100">
                    {report.description}
                  </p>
                </div>

                {/* Reporter Info */}
                <div className="mb-5 pb-4 border-b border-slate-200">
                  <p className="text-xs font-medium text-slate-600 mb-2">Reported by</p>
                  <p className="text-sm text-slate-700 truncate font-medium">{report.reporterEmail}</p>
                  <p className="text-xs text-slate-500 mt-1">
                    {new Date(report.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Admin Notes */}
                {report.adminNotes && (
                  <div className="mb-5">
                    <p className="text-xs font-medium text-slate-600 mb-2">Admin Notes</p>
                    <p className="text-sm text-slate-800 bg-blue-50 p-3 rounded-lg border border-blue-100 text-xs line-clamp-2">
                      {report.adminNotes}
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {report.status.toUpperCase() === 'PENDING' && (
                    <>
                      <button
                        onClick={() => {
                          setSelectedReport(report)
                          setShowModal(true)
                        }}
                        className="flex-1 px-4 py-2.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-sm"
                      >
                        Review
                      </button>
                      <button
                        onClick={() => confirmReportAction('dismiss', report.id)}
                        className="flex-1 px-4 py-2.5 text-sm bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors font-semibold border border-slate-200"
                      >
                        Dismiss
                      </button>
                    </>
                  )}
                  {report.status.toUpperCase() === 'REVIEWED' && (
                    <button
                      onClick={() => handleReportAction(report.id, 'resolve')}
                      className="w-full px-4 py-2.5 text-sm bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors font-semibold shadow-sm"
                    >
                      Resolve
                    </button>
                  )}
                  {(report.status.toUpperCase() === 'RESOLVED' || report.status.toUpperCase() === 'DISMISSED') && (
                    <div className="w-full px-4 py-2.5 text-sm text-center text-slate-600 bg-slate-50 rounded-lg border border-slate-200 font-medium">
                      {report.status.toUpperCase() === 'RESOLVED' ? 'Resolved' : 'Dismissed'}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Review Modal */}
      {showModal && selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto border border-slate-200">
            {/* Modal Header */}
            <div className="px-6 py-5 border-b border-slate-200 bg-slate-50">
              <div className="flex items-center gap-3">
                {getTypeIcon(selectedReport.reason)}
                <div>
                  <h3 className="text-xl font-bold text-slate-900">Review Report</h3>
                  <p className="text-sm text-slate-600 font-medium">{formatReportType(selectedReport.reason)}</p>
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="px-6 py-6 space-y-6">
              {/* Report Details */}
              <div className="bg-slate-50 rounded-lg p-5 border border-slate-200">
                <div className="grid grid-cols-2 gap-6 text-sm">
                  <div>
                    <p className="font-semibold text-slate-700 mb-2">Reported User</p>
                    <p className="text-slate-900 font-medium">{selectedReport.reportedUserName || 'No name'}</p>
                    <p className="text-slate-600 text-xs mt-1">{selectedReport.reportedUserEmail}</p>
                  </div>
                  <div>
                    <p className="font-semibold text-slate-700 mb-2">Reported By</p>
                    <p className="text-slate-700 font-medium">{selectedReport.reporterEmail}</p>
                    <p className="text-slate-500 text-xs mt-1">
                      {new Date(selectedReport.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <p className="font-semibold text-slate-800 mb-3">Report Description</p>
                <div className="bg-white border border-slate-300 rounded-lg p-4 text-sm text-slate-800">
                  {selectedReport.description}
                </div>
              </div>

              {/* Admin Notes Input */}
              <div>
                <label htmlFor="adminNotes" className="block font-semibold text-slate-800 mb-3">
                  Admin Review Notes
                </label>
                <textarea
                  className="w-full p-4 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-slate-900 bg-white"
                  rows={4}
                  placeholder="Enter your review notes and any actions taken..."
                  id="adminNotes"
                />
                <p className="text-xs text-slate-600 mt-2">
                  These notes will be saved with the report for future reference.
                </p>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="px-6 py-5 border-t border-slate-200 bg-slate-50">
              <div className="flex gap-3 mb-3">
                <button
                  onClick={() => window.open(`/admin/users/${selectedReport.reportedUserId}`, '_blank')}
                  className="flex-1 px-4 py-2.5 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors font-medium text-sm"
                >
                  View User Profile
                </button>
                <button
                  onClick={() => confirmReportAction('dismiss', selectedReport.id)}
                  className="flex-1 px-4 py-2.5 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-medium text-sm border border-red-200"
                >
                  Dismiss Report
                </button>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => {
                    const notes = (document.getElementById('adminNotes') as HTMLTextAreaElement)?.value
                    handleReportAction(selectedReport.id, 'review', notes)
                  }}
                  className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-sm"
                >
                  Mark as Reviewed
                </button>
                <button
                  onClick={() => {
                    setShowModal(false)
                    setSelectedReport(null)
                  }}
                  className="flex-1 px-4 py-3 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors font-semibold border border-slate-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDialog && confirmAction && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full border border-slate-200">
            <div className="px-6 py-5 border-b border-slate-200">
              <h3 className="text-lg font-bold text-slate-900">Confirm Action</h3>
            </div>
            <div className="px-6 py-5">
              <p className="text-slate-700">
                Are you sure you want to {confirmAction.action} this report? This action cannot be undone.
              </p>
            </div>
            <div className="px-6 py-5 border-t border-slate-200 bg-slate-50 flex gap-3">
              <button
                onClick={executeConfirmedAction}
                className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold shadow-sm"
              >
                Yes, {confirmAction.action}
              </button>
              <button
                onClick={() => {
                  setShowConfirmDialog(false)
                  setConfirmAction(null)
                }}
                className="flex-1 px-4 py-3 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors font-semibold border border-slate-200"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

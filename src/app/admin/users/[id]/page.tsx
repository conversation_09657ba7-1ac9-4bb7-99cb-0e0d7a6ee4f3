'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ArrowLeft, User, Mail, MapPin, Calendar, Heart, Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import Image from 'next/image'

interface UserProfile {
  id: string
  email: string
  name?: string
  isActive: boolean
  isVerified: boolean
  subscriptionStatus: string
  createdAt: string
  lastActive?: string
  profile?: {
    fullName: string
    gender: string
    dateOfBirth: string
    town: { name: string }
    tribe?: string
    religion?: string
    occupation?: string
    education?: string
    relationshipGoal?: string
    bio?: string
    profilePictures: { url: string; isPrimary: boolean }[]
  }
  subscription?: {
    status: string
    expiresAt?: string
    amount?: number
  }
  _count?: {
    sentReports: number
    receivedReports: number
    matches: number
    sentMessages: number
  }
}

export default function AdminUserProfile() {
  const params = useParams()
  const router = useRouter()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (params.id) {
      fetchUserProfile(params.id as string)
    }
  }, [params.id])

  const fetchUserProfile = async (userId: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch(`/api/admin/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setError('Failed to load user profile')
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      setError('Failed to load user profile')
    } finally {
      setLoading(false)
    }
  }

  const handleUserAction = async (action: string) => {
    if (!user) return
    
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch(`/api/admin/users/${user.id}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        // Refresh user data
        fetchUserProfile(user.id)
      }
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
    }
  }

  const getStatusBadge = (user: UserProfile) => {
    if (!user.isActive) {
      return <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-medium bg-red-100 text-red-700 rounded-full">
        <XCircle className="w-4 h-4" />
        Inactive
      </span>
    }
    if (user.subscriptionStatus === 'ACTIVE') {
      return <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-medium bg-green-100 text-green-700 rounded-full">
        <CheckCircle className="w-4 h-4" />
        Premium
      </span>
    }
    return <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-medium bg-gray-100 text-gray-700 rounded-full">
      <User className="w-4 h-4" />
      Free
    </span>
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Loading...</h1>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="animate-pulse space-y-4">
            <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3 mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 mb-2">Error Loading Profile</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">User Profile</h1>
            <p className="text-gray-600 mt-1">Detailed user information and management</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {user.isActive ? (
            <button
              onClick={() => handleUserAction('deactivate')}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
            >
              Deactivate User
            </button>
          ) : (
            <button
              onClick={() => handleUserAction('activate')}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              Activate User
            </button>
          )}
        </div>
      </div>

      {/* Profile Overview */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Profile Picture */}
            <div className="flex-shrink-0">
              <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
                {user.profile?.profilePictures?.[0]?.url ? (
                  <Image
                    src={user.profile.profilePictures[0].url}
                    alt={user.profile.fullName || user.name || 'User'}
                    width={128}
                    height={128}
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <User className="w-16 h-16 text-gray-400" />
                )}
              </div>
            </div>

            {/* Basic Info */}
            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {user.profile?.fullName || user.name || 'No name provided'}
                </h2>
                <div className="flex items-center gap-2 mt-1">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">{user.email}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-4">
                {getStatusBadge(user)}
                {user.isVerified && (
                  <span className="inline-flex items-center gap-1 px-3 py-1 text-sm font-medium bg-blue-100 text-blue-700 rounded-full">
                    <Shield className="w-4 h-4" />
                    Verified
                  </span>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">Joined: {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
                {user.lastActive && (
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Last active: {new Date(user.lastActive).toLocaleDateString()}</span>
                  </div>
                )}
                {user.profile?.town && (
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">{user.profile.town.name}</span>
                  </div>
                )}
                {user.profile?.dateOfBirth && (
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">
                      Age: {Math.floor((new Date().getTime() - new Date(user.profile.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000))}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Details */}
        {user.profile && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Details</h3>
            <div className="space-y-3 text-sm">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="font-medium text-gray-700">Gender:</span>
                  <span className="ml-2 text-gray-600">{user.profile.gender || 'Not specified'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Tribe:</span>
                  <span className="ml-2 text-gray-600">{user.profile.tribe || 'Not specified'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Religion:</span>
                  <span className="ml-2 text-gray-600">{user.profile.religion || 'Not specified'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Education:</span>
                  <span className="ml-2 text-gray-600">{user.profile.education || 'Not specified'}</span>
                </div>
                <div className="col-span-2">
                  <span className="font-medium text-gray-700">Occupation:</span>
                  <span className="ml-2 text-gray-600">{user.profile.occupation || 'Not specified'}</span>
                </div>
                <div className="col-span-2">
                  <span className="font-medium text-gray-700">Relationship Goal:</span>
                  <span className="ml-2 text-gray-600">{user.profile.relationshipGoal || 'Not specified'}</span>
                </div>
              </div>
              {user.profile.bio && (
                <div className="pt-3 border-t border-gray-200">
                  <span className="font-medium text-gray-700">Bio:</span>
                  <p className="mt-1 text-gray-600">{user.profile.bio}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Activity Stats */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Statistics</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-pink-50 rounded-lg">
              <div className="text-2xl font-bold text-pink-600">{user._count?.matches || 0}</div>
              <div className="text-sm text-gray-600">Matches</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{user._count?.sentMessages || 0}</div>
              <div className="text-sm text-gray-600">Messages Sent</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{user._count?.sentReports || 0}</div>
              <div className="text-sm text-gray-600">Reports Made</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{user._count?.receivedReports || 0}</div>
              <div className="text-sm text-gray-600">Reports Received</div>
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Info */}
      {user.subscription && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Status:</span>
              <span className="ml-2 text-gray-600">{user.subscription.status}</span>
            </div>
            {user.subscription.expiresAt && (
              <div>
                <span className="font-medium text-gray-700">Expires:</span>
                <span className="ml-2 text-gray-600">{new Date(user.subscription.expiresAt).toLocaleDateString()}</span>
              </div>
            )}
            {user.subscription.amount && (
              <div>
                <span className="font-medium text-gray-700">Amount:</span>
                <span className="ml-2 text-gray-600">KES {user.subscription.amount}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

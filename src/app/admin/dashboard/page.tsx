'use client'

import { useEffect, useState } from 'react'
import { Users, Heart, CreditCard, Flag, TrendingUp, UserCheck, MessageCircle, DollarSign } from 'lucide-react'

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  totalMatches: number
  totalMessages: number
  totalRevenue: number
  pendingReports: number
  newUsersToday: number
  matchesToday: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const response = await fetch('/api/admin/stats', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        }
      } catch (error) {
        console.error('Failed to fetch stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.totalUsers || 0,
      icon: <Users className="h-6 w-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: `+${stats?.newUsersToday || 0} today`
    },
    {
      title: 'Active Users',
      value: stats?.activeUsers || 0,
      icon: <UserCheck className="h-6 w-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: 'Last 30 days'
    },
    {
      title: 'Total Matches',
      value: stats?.totalMatches || 0,
      icon: <Heart className="h-6 w-6" />,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      change: `+${stats?.matchesToday || 0} today`
    },
    {
      title: 'Messages Sent',
      value: stats?.totalMessages || 0,
      icon: <MessageCircle className="h-6 w-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: 'All time'
    },
    {
      title: 'Total Revenue',
      value: `KES ${(stats?.totalRevenue || 0).toLocaleString()}`,
      icon: <DollarSign className="h-6 w-6" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      change: 'All time'
    },
    {
      title: 'Pending Reports',
      value: stats?.pendingReports || 0,
      icon: <Flag className="h-6 w-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: 'Needs attention'
    },
    {
      title: 'Growth Rate',
      value: '12.5%',
      icon: <TrendingUp className="h-6 w-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      change: 'This month'
    },
    {
      title: 'Payments',
      value: stats?.totalRevenue ? Math.floor(stats.totalRevenue / 499) : 0,
      icon: <CreditCard className="h-6 w-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: 'Successful'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome to KenyaMatch administration panel</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Last updated</p>
          <p className="text-sm font-medium text-gray-900">{new Date().toLocaleString()}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{card.value}</p>
                <p className="text-xs text-gray-500 mt-1">{card.change}</p>
              </div>
              <div className={`${card.bgColor} ${card.color} p-3 rounded-lg`}>
                {card.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-pink-50 rounded-lg">
              <Heart className="h-5 w-5 text-pink-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">New match created</p>
                <p className="text-xs text-gray-500">2 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">New user registered</p>
                <p className="text-xs text-gray-500">5 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
              <CreditCard className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-900">Payment received</p>
                <p className="text-xs text-gray-500">10 minutes ago</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-4">
            <button className="flex items-center gap-2 p-3 bg-pink-50 hover:bg-pink-100 rounded-lg transition-colors">
              <Users className="h-5 w-5 text-pink-600" />
              <span className="text-sm font-medium text-pink-700">View Users</span>
            </button>
            <button className="flex items-center gap-2 p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
              <CreditCard className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-purple-700">Payments</span>
            </button>
            <button className="flex items-center gap-2 p-3 bg-red-50 hover:bg-red-100 rounded-lg transition-colors">
              <Flag className="h-5 w-5 text-red-600" />
              <span className="text-sm font-medium text-red-700">Reports</span>
            </button>
            <button className="flex items-center gap-2 p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">Analytics</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

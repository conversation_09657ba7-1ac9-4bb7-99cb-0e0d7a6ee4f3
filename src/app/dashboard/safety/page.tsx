'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  Ban, 
  Flag, 
  AlertTriangle, 
  UserX, 
  Settings,
  Eye,
  EyeOff,
  Trash2,
  Unlock
} from 'lucide-react';

interface BlockedUser {
  id: string;
  name: string;
  profile: {
    profilePictures: Array<{
      url: string;
      isPrimary: boolean;
    }>;
    town: {
      name: string;
    };
  };
  blockedAt: string;
}

interface Report {
  id: string;
  reportedUser: {
    id: string;
    name: string;
    profile: {
      profilePictures: Array<{
        url: string;
        isPrimary: boolean;
      }>;
    };
  };
  reason: string;
  description: string;
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  createdAt: string;
  updatedAt: string;
}

export default function SafetyPage() {
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('blocked');
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportReason, setReportReason] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);

  useEffect(() => {
    fetchBlockedUsers();
    fetchReports();
  }, []);

  const fetchBlockedUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/safety/blocked-users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBlockedUsers(data.blockedUsers);
      }
    } catch (error) {
      console.error('Error fetching blocked users:', error);
    }
  };

  const fetchReports = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/safety/reports', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setReports(data.reports);
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
    } finally {
      setLoading(false);
    }
  };

  const unblockUser = async (userId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/safety/unblock/${userId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setBlockedUsers(prev => prev.filter(user => user.id !== userId));
      }
    } catch (error) {
      console.error('Error unblocking user:', error);
    }
  };

  const submitReport = async () => {
    if (!selectedUser || !reportReason || !reportDescription) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/safety/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          reportedUserId: selectedUser.id,
          reason: reportReason,
          description: reportDescription
        })
      });

      if (response.ok) {
        setShowReportDialog(false);
        setReportReason('');
        setReportDescription('');
        setSelectedUser(null);
        fetchReports(); // Refresh reports list
      }
    } catch (error) {
      console.error('Error submitting report:', error);
    }
  };

  const getReportStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      investigating: 'bg-blue-100 text-blue-800',
      resolved: 'bg-green-100 text-green-800',
      dismissed: 'bg-gray-100 text-gray-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Safety & Privacy</h1>
          <p className="text-muted-foreground">
            Manage your safety settings and report inappropriate behavior
          </p>
        </div>
        <Button variant="outline">
          <Settings className="w-4 h-4 mr-2" />
          Privacy Settings
        </Button>
      </div>

      {/* Safety Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Blocked Users</p>
                <p className="text-2xl font-bold">{blockedUsers.length}</p>
              </div>
              <UserX className="w-4 h-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Reports Submitted</p>
                <p className="text-2xl font-bold">{reports.length}</p>
              </div>
              <Flag className="w-4 h-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Reports</p>
                <p className="text-2xl font-bold">
                  {reports.filter(r => r.status === 'pending' || r.status === 'investigating').length}
                </p>
              </div>
              <AlertTriangle className="w-4 h-4 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="blocked">Blocked Users</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="guidelines">Safety Guidelines</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          <TabsContent value="blocked" className="space-y-4">
            {blockedUsers.length === 0 ? (
              <div className="text-center py-8">
                <UserX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No blocked users</h3>
                <p className="text-muted-foreground">
                  You haven't blocked any users yet.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {blockedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage 
                          src={user.profile.profilePictures.find(p => p.isPrimary)?.url} 
                          alt={user.name}
                        />
                        <AvatarFallback>
                          {user.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-semibold">{user.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {user.profile.town.name} • Blocked {formatTime(user.blockedAt)}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => unblockUser(user.id)}
                    >
                      <Unlock className="w-4 h-4 mr-2" />
                      Unblock
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            {reports.length === 0 ? (
              <div className="text-center py-8">
                <Flag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No reports submitted</h3>
                <p className="text-muted-foreground">
                  You haven't submitted any reports yet.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {reports.map((report) => (
                  <div key={report.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage 
                            src={report.reportedUser.profile.profilePictures.find(p => p.isPrimary)?.url} 
                            alt={report.reportedUser.name}
                          />
                          <AvatarFallback>
                            {report.reportedUser.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h4 className="font-semibold">{report.reportedUser.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Reported {formatTime(report.createdAt)}
                          </p>
                        </div>
                      </div>
                      {getReportStatusBadge(report.status)}
                    </div>
                    
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium">Reason:</span>
                        <span className="text-sm ml-2">{report.reason}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium">Description:</span>
                        <p className="text-sm text-muted-foreground mt-1">
                          {report.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="guidelines" className="space-y-6">
            <div className="prose max-w-none">
              <h3 className="text-lg font-semibold mb-4">Community Safety Guidelines</h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Be Respectful</h4>
                  <p className="text-blue-800 text-sm">
                    Treat all members with respect and kindness. Harassment, hate speech, and abusive behavior are not tolerated.
                  </p>
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">Be Authentic</h4>
                  <p className="text-green-800 text-sm">
                    Use recent photos and provide accurate information about yourself. Fake profiles will be removed.
                  </p>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-900 mb-2">Stay Safe</h4>
                  <p className="text-yellow-800 text-sm">
                    Meet in public places for first dates, trust your instincts, and report any suspicious behavior.
                  </p>
                </div>

                <div className="p-4 bg-red-50 rounded-lg">
                  <h4 className="font-semibold text-red-900 mb-2">No Inappropriate Content</h4>
                  <p className="text-red-800 text-sm">
                    Explicit content, spam, and commercial solicitations are prohibited and will result in account suspension.
                  </p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">How to Report</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm text-gray-700">
                  <li>Click the flag icon on any profile or message</li>
                  <li>Select the reason for your report</li>
                  <li>Provide additional details if needed</li>
                  <li>Submit the report for review</li>
                  <li>Our team will investigate within 24 hours</li>
                </ol>
              </div>
            </div>
          </TabsContent>
        </CardContent>
      </Card>

      {/* Report Dialog */}
      <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Report User</DialogTitle>
          </DialogHeader>
          
          {selectedUser && (
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Avatar className="w-10 h-10">
                  <AvatarImage 
                    src={selectedUser.profile?.profilePictures?.find(p => p.isPrimary)?.url} 
                    alt={selectedUser.name}
                  />
                  <AvatarFallback>
                    {selectedUser.name?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-semibold">{selectedUser.name}</h4>
                  <p className="text-sm text-muted-foreground">Report this user</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Reason for Report</label>
                <Select value={reportReason} onValueChange={setReportReason}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inappropriate_content">Inappropriate Content</SelectItem>
                    <SelectItem value="harassment">Harassment</SelectItem>
                    <SelectItem value="fake_profile">Fake Profile</SelectItem>
                    <SelectItem value="spam">Spam</SelectItem>
                    <SelectItem value="underage">Underage User</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Additional Details</label>
                <Textarea
                  placeholder="Please provide more details about the issue..."
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                  rows={4}
                  className="text-gray-900 bg-white placeholder-gray-400"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowReportDialog(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={submitReport}
                  disabled={!reportReason || !reportDescription}
                  className="flex-1"
                >
                  Submit Report
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 
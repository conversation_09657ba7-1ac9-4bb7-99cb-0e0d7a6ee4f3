'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Search, 
  Filter, 
  MapPin, 
  Heart, 
  MessageCircle, 
  Eye,
  X,
  RefreshCw,
  Star
} from 'lucide-react';

interface Match {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    profile: {
      age: number;
      gender: string;
      town: {
        name: string;
      };
      bio: string;
      profilePictures: Array<{
        url: string;
        isPrimary: boolean;
      }>;
      hasChildren: boolean;
      childrenCount?: number;
      education: string;
      occupation: string;
      religion: string;
      relationshipGoal: string;
    };
  };
  compatibility: number;
  distance: number;
  isLiked: boolean;
  isMatched: boolean;
}

interface Filters {
  ageRange: [number, number];
  distance: number;
  gender: string;
  hasChildren: string;
  education: string;
  religion: string;
  relationshipGoal: string;
  town: string;
  onlineOnly: boolean;
  verifiedOnly: boolean;
}

export default function SearchPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    ageRange: [18, 65],
    distance: 50,
    gender: 'all',
    hasChildren: 'all',
    education: 'all',
    religion: 'all',
    relationshipGoal: 'all',
    town: '',
    onlineOnly: false,
    verifiedOnly: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('compatibility');

  useEffect(() => {
    fetchMatches();
  }, [filters, sortBy]);

  const fetchMatches = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const queryParams = new URLSearchParams({
        ...filters,
        ageMin: filters.ageRange[0].toString(),
        ageMax: filters.ageRange[1].toString(),
        sortBy
      });

      const response = await fetch(`/api/matches/search?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMatches(data.matches);
      }
    } catch (error) {
      console.error('Error fetching matches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (matchId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/matches/like', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ matchId })
      });

      if (response.ok) {
        // Update the match in the list
        setMatches(prev => prev.map(match => 
          match.id === matchId 
            ? { ...match, isLiked: true }
            : match
        ));
      }
    } catch (error) {
      console.error('Error liking match:', error);
    }
  };

  const handlePass = async (matchId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/matches/pass', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ matchId })
      });

      if (response.ok) {
        // Remove the match from the list
        setMatches(prev => prev.filter(match => match.id !== matchId));
      }
    } catch (error) {
      console.error('Error passing match:', error);
    }
  };

  const clearFilters = () => {
    setFilters({
      ageRange: [18, 65],
      distance: 50,
      gender: 'all',
      hasChildren: 'all',
      education: 'all',
      religion: 'all',
      relationshipGoal: 'all',
      town: '',
      onlineOnly: false,
      verifiedOnly: false
    });
  };

  const filteredMatches = matches.filter(match =>
    match.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    match.user.profile.town.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    match.user.profile.bio.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Find Your Match</h1>
          <p className="text-muted-foreground">
            Discover people who share your interests and values
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
          <Button
            variant="outline"
            onClick={clearFilters}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Clear
          </Button>
        </div>
      </div>

      {/* Search and Sort */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name, location, or bio..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="compatibility">Compatibility</SelectItem>
            <SelectItem value="distance">Distance</SelectItem>
            <SelectItem value="age">Age</SelectItem>
            <SelectItem value="recent">Recently Active</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Filters</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Age Range */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Age Range: {filters.ageRange[0]} - {filters.ageRange[1]}
                </label>
                <Slider
                  value={filters.ageRange}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, ageRange: value as [number, number] }))}
                  max={65}
                  min={18}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Distance */}
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Distance: {filters.distance} km
                </label>
                <Slider
                  value={[filters.distance]}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, distance: value[0] }))}
                  max={100}
                  min={5}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Gender */}
              <div>
                <label className="text-sm font-medium mb-2 block">Gender</label>
                <Select value={filters.gender} onValueChange={(value) => setFilters(prev => ({ ...prev, gender: value }))}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genders</SelectItem>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Has Children */}
              <div>
                <label className="text-sm font-medium mb-2 block">Children</label>
                <Select value={filters.hasChildren} onValueChange={(value) => setFilters(prev => ({ ...prev, hasChildren: value }))}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any</SelectItem>
                    <SelectItem value="yes">Has Children</SelectItem>
                    <SelectItem value="no">No Children</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Education */}
              <div>
                <label className="text-sm font-medium mb-2 block">Education</label>
                <Select value={filters.education} onValueChange={(value) => setFilters(prev => ({ ...prev, education: value }))}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any Education</SelectItem>
                    <SelectItem value="high_school">High School</SelectItem>
                    <SelectItem value="college">College</SelectItem>
                    <SelectItem value="university">University</SelectItem>
                    <SelectItem value="postgraduate">Postgraduate</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Religion */}
              <div>
                <label className="text-sm font-medium mb-2 block">Religion</label>
                <Select value={filters.religion} onValueChange={(value) => setFilters(prev => ({ ...prev, religion: value }))}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any Religion</SelectItem>
                    <SelectItem value="christian">Christian</SelectItem>
                    <SelectItem value="muslim">Muslim</SelectItem>
                    <SelectItem value="hindu">Hindu</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Relationship Goal */}
              <div>
                <label className="text-sm font-medium mb-2 block">Relationship Goal</label>
                <Select value={filters.relationshipGoal} onValueChange={(value) => setFilters(prev => ({ ...prev, relationshipGoal: value }))}>
                  <SelectTrigger className="text-gray-900 bg-white placeholder-gray-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Any Goal</SelectItem>
                    <SelectItem value="marriage">Marriage</SelectItem>
                    <SelectItem value="serious_relationship">Serious Relationship</SelectItem>
                    <SelectItem value="casual_dating">Casual Dating</SelectItem>
                    <SelectItem value="friendship">Friendship</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="onlineOnly"
                    checked={filters.onlineOnly}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, onlineOnly: checked as boolean }))}
                  />
                  <label htmlFor="onlineOnly" className="text-sm font-medium">
                    Online Only
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="verifiedOnly"
                    checked={filters.verifiedOnly}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, verifiedOnly: checked as boolean }))}
                  />
                  <label htmlFor="verifiedOnly" className="text-sm font-medium">
                    Verified Profiles Only
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMatches.map((match) => (
          <Card key={match.id} className="overflow-hidden">
            <div className="relative">
              <img
                src={match.user.profile.profilePictures.find(p => p.isPrimary)?.url || '/placeholder-avatar.jpg'}
                alt={match.user.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 right-2 flex gap-1">
                <Badge className="bg-primary text-white">
                  {match.compatibility}% Match
                </Badge>
                {match.user.profile.verified && (
                  <Badge className="bg-blue-500 text-white">
                    <Star className="w-3 h-3" />
                  </Badge>
                )}
              </div>
            </div>
            
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold text-lg">{match.user.name}, {match.user.profile.age}</h3>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <MapPin className="w-3 h-3 mr-1" />
                    {match.user.profile.town.name} • {match.distance}km away
                  </p>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {match.user.profile.bio}
              </p>
              
              <div className="flex flex-wrap gap-1 mb-3">
                <Badge variant="secondary" className="text-xs">
                  {match.user.profile.occupation}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {match.user.profile.education}
                </Badge>
                {match.user.profile.hasChildren && (
                  <Badge variant="secondary" className="text-xs">
                    Has Children
                  </Badge>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => handlePass(match.id)}
                >
                  <X className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => handleLike(match.id)}
                  disabled={match.isLiked}
                >
                  <Heart className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`/profile/${match.user.id}`, '_blank')}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredMatches.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No matches found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your filters or expanding your search criteria
              </p>
              <Button onClick={clearFilters}>
                Clear All Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 
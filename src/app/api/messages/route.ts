import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const matchId = searchParams.get('matchId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const skip = (page - 1) * limit

    if (!matchId) {
      return NextResponse.json(
        { success: false, error: 'Match ID is required' },
        { status: 400 }
      )
    }

    // Verify user is part of the match
    const match = await prisma.match.findFirst({
      where: {
        id: matchId,
        OR: [
          { user1Id: decoded.userId },
          { user2Id: decoded.userId }
        ]
      }
    })

    if (!match) {
      return NextResponse.json(
        { success: false, error: 'Match not found' },
        { status: 404 }
      )
    }

    // Check if user has active subscription
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    })

    if (!user?.isPaid || !user.subscriptionExpiresAt || new Date() > user.subscriptionExpiresAt) {
      return NextResponse.json(
        { success: false, error: 'Active subscription required to view messages' },
        { status: 403 }
      )
    }

    // Get messages
    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where: { matchId },
        include: {
          sender: {
            select: {
              id: true,
              profile: {
                select: {
                  fullName: true,
                  profilePictures: {
                    where: { isPrimary: true },
                    take: 1,
                    select: { url: true }
                  }
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.message.count({
        where: { matchId }
      })
    ])

    // Mark messages as read
    await prisma.message.updateMany({
      where: {
        matchId,
        receiverId: decoded.userId,
        isRead: false
      },
      data: { isRead: true }
    })

    return NextResponse.json({
      success: true,
      data: messages.reverse(), // Return in chronological order
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get messages error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { matchId, content } = body

    if (!matchId || !content) {
      return NextResponse.json(
        { success: false, error: 'Match ID and content are required' },
        { status: 400 }
      )
    }

    // Verify user is part of the match
    const match = await prisma.match.findFirst({
      where: {
        id: matchId,
        OR: [
          { user1Id: decoded.userId },
          { user2Id: decoded.userId }
        ]
      }
    })

    if (!match) {
      return NextResponse.json(
        { success: false, error: 'Match not found' },
        { status: 404 }
      )
    }

    // Check if user has active subscription
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    })

    if (!user?.isPaid || !user.subscriptionExpiresAt || new Date() > user.subscriptionExpiresAt) {
      return NextResponse.json(
        { success: false, error: 'Active subscription required to send messages' },
        { status: 403 }
      )
    }

    // Determine receiver
    const receiverId = match.user1Id === decoded.userId ? match.user2Id : match.user1Id

    // Create message
    const message = await prisma.message.create({
      data: {
        matchId,
        senderId: decoded.userId,
        receiverId,
        content: content.trim()
      },
      include: {
        sender: {
          select: {
            id: true,
            profile: {
              select: {
                fullName: true,
                profilePictures: {
                  where: { isPrimary: true },
                  take: 1,
                  select: { url: true }
                }
              }
            }
          }
        }
      }
    })

    // Create notification for receiver
    await prisma.notification.create({
      data: {
        userId: receiverId,
        type: 'message',
        title: 'New Message',
        message: `You have a new message from ${message.sender.profile?.fullName || 'Someone'}`,
        metadata: {
          matchId,
          messageId: message.id,
          senderId: decoded.userId
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: message,
      message: 'Message sent successfully'
    })

  } catch (error) {
    console.error('Send message error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 
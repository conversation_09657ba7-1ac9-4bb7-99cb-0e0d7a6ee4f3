import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const blockedUserId = params.userId;

    // Check if user is trying to unblock themselves
    if (payload.userId === blockedUserId) {
      return NextResponse.json(
        { error: 'Cannot unblock yourself' },
        { status: 400 }
      );
    }

    // Remove the block
    await prisma.blockedUser.deleteMany({
      where: {
        blockerId: payload.userId,
        blockedId: blockedUserId
      }
    });

    return NextResponse.json({ 
      message: 'User unblocked successfully' 
    });

  } catch (error) {
    console.error('Error unblocking user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch blocked users
    const blockedUsers = await prisma.blockedUser.findMany({
      where: {
        blockerId: payload.userId
      },
      include: {
        blocked: {
          select: {
            id: true,
            name: true,
            profile: {
              select: {
                profilePictures: {
                  where: { isPrimary: true },
                  select: { url: true, isPrimary: true }
                },
                town: {
                  select: { name: true }
                }
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Transform the data to match the expected format
    const transformedUsers = blockedUsers.map((block: any) => ({
      id: block.blocked.id,
      name: block.blocked.name,
      profile: block.blocked.profile,
      blockedAt: block.createdAt.toISOString()
    }));

    return NextResponse.json({ blockedUsers: transformedUsers });

  } catch (error) {
    console.error('Error fetching blocked users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
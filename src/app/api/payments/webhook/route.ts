import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { calculateSubscriptionExpiry } from '@/lib/payments'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = request.headers.get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { success: false, error: 'Missing signature' },
        { status: 400 }
      )
    }

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { success: false, error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSuccess(event.data.object as Stripe.PaymentIntent)
        break
      case 'payment_intent.payment_failed':
        await handlePaymentFailure(event.data.object as Stripe.PaymentIntent)
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Find payment record
    const payment = await prisma.payment.findFirst({
      where: {
        transactionId: paymentIntent.id,
        status: 'PENDING'
      },
      include: {
        user: true
      }
    })

    if (!payment) {
      console.error('Payment record not found:', paymentIntent.id)
      return
    }

    // Update payment status
    await prisma.payment.update({
      where: { id: payment.id },
      data: { status: 'COMPLETED' }
    })

    // Update user subscription
    const expiryDate = calculateSubscriptionExpiry()
    await prisma.user.update({
      where: { id: payment.userId },
      data: {
        isPaid: true,
        subscriptionExpiresAt: expiryDate
      }
    })

    // Create notification
    await prisma.notification.create({
      data: {
        userId: payment.userId,
        type: 'payment',
        title: 'Payment Successful',
        message: 'Your subscription has been activated successfully! You can now access matches and chat features.',
        metadata: {
          paymentId: payment.id,
          amount: payment.amount,
          expiryDate
        }
      }
    })

    console.log('Payment processed successfully:', paymentIntent.id)

  } catch (error) {
    console.error('Error processing payment success:', error)
  }
}

async function handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Find payment record
    const payment = await prisma.payment.findFirst({
      where: {
        transactionId: paymentIntent.id,
        status: 'PENDING'
      }
    })

    if (!payment) {
      console.error('Payment record not found:', paymentIntent.id)
      return
    }

    // Update payment status
    await prisma.payment.update({
      where: { id: payment.id },
      data: { status: 'FAILED' }
    })

    // Create notification
    await prisma.notification.create({
      data: {
        userId: payment.userId,
        type: 'payment',
        title: 'Payment Failed',
        message: 'Your payment was unsuccessful. Please try again.',
        metadata: {
          paymentId: payment.id,
          amount: payment.amount
        }
      }
    })

    console.log('Payment failure processed:', paymentIntent.id)

  } catch (error) {
    console.error('Error processing payment failure:', error)
  }
} 
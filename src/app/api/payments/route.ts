import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/auth'
import { createStripePaymentIntent, createMpesaPayment, calculateSubscriptionExpiry } from '@/lib/payments'

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { method, phoneNumber } = body

    const amount = parseInt(process.env.SUBSCRIPTION_PRICE || '1200')

    // Check if user already has active subscription
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    })

    if (user?.isPaid && user.subscriptionExpiresAt && new Date() < user.subscriptionExpiresAt) {
      return NextResponse.json(
        { success: false, error: 'User already has an active subscription' },
        { status: 400 }
      )
    }

    let paymentIntent
    let transactionId

    if (method === 'STRIPE') {
      // Create Stripe payment intent
      paymentIntent = await createStripePaymentIntent(amount)
      transactionId = paymentIntent.id
    } else if (method === 'MPESA') {
      if (!phoneNumber) {
        return NextResponse.json(
          { success: false, error: 'Phone number is required for M-Pesa payment' },
          { status: 400 }
        )
      }

      // Create M-Pesa payment
      const mpesaResponse = await createMpesaPayment(phoneNumber, amount)
      transactionId = mpesaResponse.CheckoutRequestID
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid payment method' },
        { status: 400 }
      )
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        userId: decoded.userId,
        amount,
        method,
        status: 'PENDING',
        transactionId,
        metadata: {
          paymentIntentId: paymentIntent?.id,
          method,
          phoneNumber
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        paymentId: payment.id,
        amount,
        method,
        clientSecret: paymentIntent?.client_secret,
        transactionId
      },
      message: 'Payment initiated successfully'
    })

  } catch (error) {
    console.error('Payment error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where: { userId: decoded.userId },
        orderBy: { paidAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.payment.count({
        where: { userId: decoded.userId }
      })
    ])

    return NextResponse.json({
      success: true,
      data: payments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get payments error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT, isSubscriptionActive } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        isPaid: true,
        subscriptionExpiresAt: true,
        profile: {
          select: {
            fullName: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    const subscriptionActive = isSubscriptionActive(user)

    return NextResponse.json({
      success: true,
      isActive: subscriptionActive,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.profile?.fullName,
        isPaid: user.isPaid,
        subscriptionExpiresAt: user.subscriptionExpiresAt
      }
    })

  } catch (error) {
    console.error('Subscription status check error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

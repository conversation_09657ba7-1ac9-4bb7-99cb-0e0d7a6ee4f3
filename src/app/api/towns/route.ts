import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const county = searchParams.get('county')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { county: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (county) {
      where.county = county
    }

    const towns = await prisma.town.findMany({
      where,
      orderBy: [
        { county: 'asc' },
        { name: 'asc' }
      ]
    })

    return NextResponse.json({
      success: true,
      data: towns
    })

  } catch (error) {
    console.error('Get towns error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('[API] No or invalid Authorization header:', authHeader);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    console.log('[API] Received token:', token);
    const payload = verifyJWT(token);
    console.log('[API] Decoded payload:', payload);
    
    if (!payload?.userId) {
      console.log('[API] Invalid token payload:', payload);
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Fetch dashboard data
    const [
      totalUsers,
      activeSubscriptions,
      totalPayments,
      pendingReports,
      recentActivity
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Active subscriptions (users with paid status and valid expiry)
      prisma.user.count({
        where: {
          isPaid: true,
          subscriptionExpiresAt: {
            gt: now
          }
        }
      }),
      
      // Total payments
      prisma.payment.count({
        where: {
          status: 'COMPLETED',
          paidAt: {
            gte: startDate
          }
        }
      }),
      
      // Pending reports
      prisma.report.count({
        where: {
          status: 'pending'
        }
      }),
      
      // Recent activity (last 10 activities)
      prisma.userActivity.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          targetUser: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
    ]);

    // Transform recent activity
    const transformedActivity = recentActivity.map((activity: any) => {
      let description = '';
      let type = 'general';

      switch (activity.activityType) {
        case 'profile_view':
          description = `${activity.user?.name || 'User'} viewed a profile`;
          type = 'user';
          break;
        case 'like':
          description = `${activity.user?.name || 'User'} liked a profile`;
          type = 'user';
          break;
        case 'match':
          description = `${activity.user?.name || 'User'} matched with ${activity.targetUser?.name || 'another user'}`;
          type = 'match';
          break;
        case 'message':
          description = `${activity.user?.name || 'User'} sent a message`;
          type = 'message';
          break;
        case 'payment':
          description = `${activity.user?.name || 'User'} made a payment`;
          type = 'payment';
          break;
        case 'login':
          description = `${activity.user?.name || 'User'} logged in`;
          type = 'user';
          break;
        default:
          description = `${activity.user?.name || 'User'} performed an action`;
          type = 'general';
      }

      return {
        id: activity.id,
        type,
        description,
        timestamp: activity.createdAt.toISOString()
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        totalUsers,
        activeSubscriptions,
        totalPayments,
        pendingReports,
        recentActivity: transformedActivity
      }
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
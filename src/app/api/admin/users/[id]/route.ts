import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Await params to comply with Next.js 15 requirements
    const { id } = await params

    // Verify admin authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.substring(7)
    const payload = verifyJWT(token)
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const adminUser = await prisma.user.findUnique({
      where: { id: payload.userId }
    })

    if (!adminUser || adminUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Fetch the user with detailed information
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        profile: {
          include: {
            town: true,
            profilePictures: {
              orderBy: { isPrimary: 'desc' }
            }
          }
        },
        payments: {
          where: { status: 'COMPLETED' },
          orderBy: { paidAt: 'desc' },
          take: 5,
          include: {
            plan: true
          }
        },
        _count: {
          select: {
            reportsMade: true,
            reportsReceived: true,
            matchesInitiated: true,
            messagesSent: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Determine subscription status
    const isSubscriptionActive = user.isPaid && user.subscriptionExpiresAt && new Date(user.subscriptionExpiresAt) > new Date()
    const subscriptionStatus = isSubscriptionActive ? 'ACTIVE' : 'INACTIVE'

    // Transform the data to match the expected format
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      isActive: user.isActive,
      isVerified: user.profile?.verified || false,
      subscriptionStatus: subscriptionStatus,
      createdAt: user.createdAt.toISOString(),
      lastActive: user.profile?.lastActive?.toISOString() || user.lastLoginAt?.toISOString(),
      profile: user.profile ? {
        fullName: user.profile.fullName,
        gender: user.profile.gender,
        dateOfBirth: user.profile.dateOfBirth?.toISOString(),
        town: user.profile.town,
        tribe: user.profile.tribe,
        religion: user.profile.religion,
        occupation: user.profile.occupation,
        education: user.profile.education,
        relationshipGoal: user.profile.relationshipGoal,
        bio: user.profile.bio,
        profilePictures: user.profile.profilePictures.map(pic => ({
          url: pic.url,
          isPrimary: pic.isPrimary
        }))
      } : null,
      subscription: user.isPaid ? {
        status: subscriptionStatus,
        expiresAt: user.subscriptionExpiresAt?.toISOString(),
        amount: user.payments?.[0]?.amount || null
      } : null,
      _count: {
        sentReports: user._count.reportsMade,
        receivedReports: user._count.reportsReceived,
        matches: user._count.matchesInitiated,
        sentMessages: user._count.messagesSent
      }
    }

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('Get user profile error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

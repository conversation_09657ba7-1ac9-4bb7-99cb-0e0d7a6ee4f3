import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Fetch analytics data
    const [
      totalUsers,
      activeUsers,
      totalMatches,
      totalMessages,
      totalRevenue,
      previousPeriodUsers,
      previousPeriodRevenue,
      topTowns,
      ageDistribution,
      genderDistribution,
      monthlyStats
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Active users (users with activity in last 30 days)
      prisma.user.count({
        where: {
          OR: [
            { lastLoginAt: { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } },
            { profile: { updatedAt: { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } } }
          ]
        }
      }),
      
      // Total matches
      prisma.match.count(),
      
      // Total messages
      prisma.message.count(),
      
      // Total revenue
      prisma.payment.aggregate({
        where: { status: 'completed' },
        _sum: { amount: true }
      }),
      
      // Previous period users for growth calculation
      prisma.user.count({
        where: { createdAt: { lt: startDate } }
      }),
      
      // Previous period revenue for growth calculation
      prisma.payment.aggregate({
        where: { 
          status: 'completed',
          createdAt: { lt: startDate }
        },
        _sum: { amount: true }
      }),
      
      // Top towns
      prisma.user.groupBy({
        by: ['profile'],
        _count: { id: true },
        where: { profile: { townId: { not: null } } },
        orderBy: { _count: { id: 'desc' } },
        take: 10
      }),
      
      // Age distribution
      prisma.user.groupBy({
        by: ['profile'],
        _count: { id: true },
        where: { profile: { dateOfBirth: { not: null } } }
      }),
      
      // Gender distribution
      prisma.user.groupBy({
        by: ['profile'],
        _count: { id: true },
        where: { profile: { gender: { not: null } } }
      }),
      
      // Monthly stats (last 6 months)
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(DISTINCT u.id) as users,
          COUNT(DISTINCT m.id) as matches,
          COALESCE(SUM(p.amount), 0) as revenue
        FROM "User" u
        LEFT JOIN "Match" m ON DATE_TRUNC('month', m."createdAt") = DATE_TRUNC('month', u."createdAt")
        LEFT JOIN "Payment" p ON DATE_TRUNC('month', p."createdAt") = DATE_TRUNC('month', u."createdAt") AND p.status = 'completed'
        WHERE u."createdAt" >= NOW() - INTERVAL '6 months'
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
        LIMIT 6
      `
    ]);

    // Process top towns data
    const processedTopTowns = await Promise.all(
      topTowns.map(async (group) => {
        const town = await prisma.town.findUnique({
          where: { id: group.profile?.townId || '' }
        });
        return {
          town: town?.name || 'Unknown',
          count: group._count.id
        };
      })
    );

    // Process age distribution
    const processedAgeDistribution = ageDistribution.reduce((acc, group) => {
      if (group.profile?.dateOfBirth) {
        const age = now.getFullYear() - new Date(group.profile.dateOfBirth).getFullYear();
        let range = '';
        if (age < 25) range = '18-24';
        else if (age < 35) range = '25-34';
        else if (age < 45) range = '35-44';
        else if (age < 55) range = '45-54';
        else range = '55+';
        
        const existing = acc.find(item => item.range === range);
        if (existing) {
          existing.count += group._count.id;
        } else {
          acc.push({ range, count: group._count.id });
        }
      }
      return acc;
    }, [] as Array<{ range: string; count: number }>);

    // Process gender distribution
    const processedGenderDistribution = genderDistribution.reduce((acc, group) => {
      if (group.profile?.gender) {
        const existing = acc.find(item => item.gender === group.profile!.gender);
        if (existing) {
          existing.count += group._count.id;
        } else {
          acc.push({ gender: group.profile!.gender, count: group._count.id });
        }
      }
      return acc;
    }, [] as Array<{ gender: string; count: number }>);

    // Calculate growth percentages
    const userGrowth = previousPeriodUsers > 0 
      ? ((totalUsers - previousPeriodUsers) / previousPeriodUsers) * 100 
      : 0;
    
    const revenueGrowth = (previousPeriodRevenue._sum.amount || 0) > 0 
      ? (((totalRevenue._sum.amount || 0) - (previousPeriodRevenue._sum.amount || 0)) / (previousPeriodRevenue._sum.amount || 0)) * 100 
      : 0;

    // Calculate rates
    const matchRate = totalUsers > 0 ? (totalMatches / totalUsers) * 100 : 0;
    const messageRate = activeUsers > 0 ? totalMessages / activeUsers : 0;

    return NextResponse.json({
      totalUsers,
      activeUsers,
      totalMatches,
      totalMessages,
      totalRevenue: totalRevenue._sum.amount || 0,
      userGrowth: Math.round(userGrowth * 100) / 100,
      matchRate: Math.round(matchRate * 100) / 100,
      messageRate: Math.round(messageRate * 100) / 100,
      revenueGrowth: Math.round(revenueGrowth * 100) / 100,
      topTowns: processedTopTowns,
      ageDistribution: processedAgeDistribution,
      genderDistribution: processedGenderDistribution,
      monthlyStats: monthlyStats as any[]
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
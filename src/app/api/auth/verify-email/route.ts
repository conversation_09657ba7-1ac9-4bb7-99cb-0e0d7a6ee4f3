import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendVerificationEmail } from '@/lib/email'
import { verifyJWT } from '@/lib/auth'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { profile: true }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if email is already verified
    const existingVerification = await prisma.userVerification.findFirst({
      where: {
        profileId: user.profile?.id,
        type: 'email',
        status: 'approved'
      }
    })

    if (existingVerification) {
      return NextResponse.json(
        { success: false, error: 'Email is already verified' },
        { status: 400 }
      )
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

    // Create or update verification record
    await prisma.userVerification.upsert({
      where: {
        profileId_type: {
          profileId: user.profile!.id,
          type: 'email'
        }
      },
      update: {
        status: 'pending',
        documentUrl: verificationToken,
        createdAt: new Date()
      },
      create: {
        profileId: user.profile!.id,
        type: 'email',
        status: 'pending',
        documentUrl: verificationToken
      }
    })

    // Send verification email
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${verificationToken}`
    
    try {
      await sendVerificationEmail(user.email, user.profile?.fullName || 'User', verificationUrl)
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError)
      // Don't fail the request if email sending fails
    }

    return NextResponse.json({
      success: true,
      message: 'Verification email sent successfully'
    })

  } catch (error) {
    console.error('Send verification email error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Verification token is required' },
        { status: 400 }
      )
    }

    // Find verification record
    const verification = await prisma.userVerification.findFirst({
      where: {
        documentUrl: token,
        type: 'email',
        status: 'pending'
      },
      include: {
        profile: {
          include: { user: true }
        }
      }
    })

    if (!verification) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired verification token' },
        { status: 400 }
      )
    }

    // Check if token is expired (24 hours)
    const tokenAge = Date.now() - verification.createdAt.getTime()
    if (tokenAge > 24 * 60 * 60 * 1000) {
      return NextResponse.json(
        { success: false, error: 'Verification token has expired' },
        { status: 400 }
      )
    }

    // Update verification status
    await prisma.userVerification.update({
      where: { id: verification.id },
      data: {
        status: 'approved',
        verifiedAt: new Date()
      }
    })

    // Update profile verification status
    await prisma.profile.update({
      where: { id: verification.profileId },
      data: { verified: true }
    })

    // Create notification
    await prisma.notification.create({
      data: {
        userId: verification.profile.user.id,
        type: 'verification',
        title: 'Email Verified',
        message: 'Your email has been successfully verified! Your profile is now more trustworthy.',
        metadata: {
          verificationType: 'email'
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Email verified successfully'
    })

  } catch (error) {
    console.error('Verify email error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { sendPasswordResetEmail } from '@/lib/email'
import crypto from 'crypto'
import { z } from 'zod'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = forgotPasswordSchema.parse(body)

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: { profile: true }
    })

    // Always return success to prevent email enumeration attacks
    const successResponse = {
      success: true,
      message: 'If an account with that email exists, we have sent a password reset link.'
    }

    if (!user) {
      return NextResponse.json(successResponse)
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

    // Store reset token in database
    await prisma.user.update({
      where: { id: user.id },
      data: {
        // Store reset token in a JSON field for now
        // In production, you might want a separate table for password resets
        updatedAt: new Date()
      }
    })

    // Create a temporary record for password reset
    // Using UserVerification table for simplicity
    await prisma.userVerification.upsert({
      where: {
        profileId_type: {
          profileId: user.profile!.id,
          type: 'password_reset'
        }
      },
      update: {
        status: 'pending',
        documentUrl: resetToken,
        createdAt: new Date()
      },
      create: {
        profileId: user.profile!.id,
        type: 'password_reset',
        status: 'pending',
        documentUrl: resetToken
      }
    })

    // Send password reset email
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`
    
    try {
      await sendPasswordResetEmail(
        user.email, 
        user.profile?.fullName || 'User', 
        resetUrl
      )
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError)
      // Don't fail the request if email sending fails
    }

    return NextResponse.json(successResponse)

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address' },
        { status: 400 }
      )
    }

    console.error('Forgot password error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

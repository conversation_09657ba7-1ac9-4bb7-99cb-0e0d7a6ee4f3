import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'
import { hashPassword } from '@/lib/auth'
import { isValidEmail, isValidPhoneNumber, formatPhoneNumber } from '@/lib/utils'

const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  genderPreference: z.enum(['MALE', 'FEMALE', 'OTHER']),
  dateOfBirth: z.string().refine((date) => {
    const age = new Date().getFullYear() - new Date(date).getFullYear()
    return age >= 18 && age <= 100
  }, 'You must be at least 18 years old'),
  townId: z.string().uuid('Invalid town ID'),
  bio: z.string().optional(),
  tribe: z.string().optional(),
  religion: z.string().optional(),
  occupation: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = registerSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: validatedData.email },
          ...(validatedData.phone ? [{ phone: formatPhoneNumber(validatedData.phone) }] : [])
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email or phone already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password)

    // Create user and profile in a transaction
    const user = await prisma.$transaction(async (tx) => {
      const newUser = await tx.user.create({
        data: {
          email: validatedData.email,
          phone: validatedData.phone ? formatPhoneNumber(validatedData.phone) : null,
          passwordHash: hashedPassword,
        }
      })

      const profile = await tx.profile.create({
        data: {
          userId: newUser.id,
          fullName: validatedData.fullName,
          gender: validatedData.gender,
          genderPreference: validatedData.genderPreference,
          dateOfBirth: new Date(validatedData.dateOfBirth),
          townId: validatedData.townId,
          bio: validatedData.bio,
          tribe: validatedData.tribe,
          religion: validatedData.religion,
          occupation: validatedData.occupation,
        }
      })

      return { ...newUser, profile }
    })

    // Remove password hash from response
    const { passwordHash, ...userWithoutPassword } = user

    return NextResponse.json({
      success: true,
      data: userWithoutPassword,
      message: 'User registered successfully'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Registration error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
} 
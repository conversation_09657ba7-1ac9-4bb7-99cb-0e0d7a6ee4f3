import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { hashPassword } from '@/lib/auth'
import { z } from 'zod'

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = resetPasswordSchema.parse(body)

    // Find the password reset verification record
    const verification = await prisma.userVerification.findFirst({
      where: {
        documentUrl: validatedData.token,
        type: 'password_reset',
        status: 'pending'
      },
      include: {
        profile: {
          include: { user: true }
        }
      }
    })

    if (!verification) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired reset token' },
        { status: 400 }
      )
    }

    // Check if token is expired (1 hour)
    const tokenAge = Date.now() - verification.createdAt.getTime()
    if (tokenAge > 60 * 60 * 1000) {
      // Clean up expired token
      await prisma.userVerification.delete({
        where: { id: verification.id }
      })
      
      return NextResponse.json(
        { success: false, error: 'Reset token has expired. Please request a new one.' },
        { status: 400 }
      )
    }

    // Hash the new password
    const hashedPassword = await hashPassword(validatedData.password)

    // Update user's password
    await prisma.user.update({
      where: { id: verification.profile.user.id },
      data: {
        passwordHash: hashedPassword,
        updatedAt: new Date()
      }
    })

    // Clean up the reset token
    await prisma.userVerification.delete({
      where: { id: verification.id }
    })

    // Create notification
    await prisma.notification.create({
      data: {
        userId: verification.profile.user.id,
        type: 'security',
        title: 'Password Changed',
        message: 'Your password has been successfully changed. If you did not make this change, please contact support immediately.',
        metadata: {
          action: 'password_reset'
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Password has been reset successfully. You can now log in with your new password.'
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Reset password error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Reset token is required' },
        { status: 400 }
      )
    }

    // Verify the token exists and is valid
    const verification = await prisma.userVerification.findFirst({
      where: {
        documentUrl: token,
        type: 'password_reset',
        status: 'pending'
      }
    })

    if (!verification) {
      return NextResponse.json(
        { success: false, error: 'Invalid reset token' },
        { status: 400 }
      )
    }

    // Check if token is expired (1 hour)
    const tokenAge = Date.now() - verification.createdAt.getTime()
    if (tokenAge > 60 * 60 * 1000) {
      return NextResponse.json(
        { success: false, error: 'Reset token has expired' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Reset token is valid'
    })

  } catch (error) {
    console.error('Verify reset token error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    // Get available filter options from the database
    const [towns, tribes, religions] = await Promise.all([
      // Get all towns
      prisma.town.findMany({
        select: {
          id: true,
          name: true,
          county: true
        },
        orderBy: { name: 'asc' }
      }),

      // Get unique tribes from profiles
      prisma.profile.findMany({
        where: {
          tribe: { not: null }
        },
        select: { tribe: true },
        distinct: ['tribe']
      }).then(results => 
        results
          .map(r => r.tribe)
          .filter(Boolean)
          .sort()
      ),

      // Get unique religions from profiles
      prisma.profile.findMany({
        where: {
          religion: { not: null }
        },
        select: { religion: true },
        distinct: ['religion']
      }).then(results => 
        results
          .map(r => r.religion)
          .filter(Boolean)
          .sort()
      )
    ])

    // Age ranges (common age brackets)
    const ageRanges = [
      { label: '18-25', min: 18, max: 25 },
      { label: '26-30', min: 26, max: 30 },
      { label: '31-35', min: 31, max: 35 },
      { label: '36-40', min: 36, max: 40 },
      { label: '41-45', min: 41, max: 45 },
      { label: '46-50', min: 46, max: 50 },
      { label: '51+', min: 51, max: 100 }
    ]

    // Gender options
    const genders = [
      { value: 'MALE', label: 'Male' },
      { value: 'FEMALE', label: 'Female' },
      { value: 'OTHER', label: 'Other' }
    ]

    // Children options
    const childrenOptions = [
      { value: 'YES', label: 'Has Children' },
      { value: 'NO', label: 'No Children' },
      { value: 'DOESNT_MATTER', label: "Doesn't Matter" }
    ]

    // Distance options (in kilometers)
    const distanceOptions = [
      { value: 10, label: 'Within 10 km' },
      { value: 25, label: 'Within 25 km' },
      { value: 50, label: 'Within 50 km' },
      { value: 100, label: 'Within 100 km' },
      { value: 200, label: 'Within 200 km' },
      { value: 500, label: 'Within 500 km' },
      { value: 1000, label: 'Anywhere in Kenya' }
    ]

    return NextResponse.json({
      success: true,
      data: {
        towns,
        tribes,
        religions,
        ageRanges,
        genders,
        childrenOptions,
        distanceOptions
      }
    })

  } catch (error) {
    console.error('Get filter options error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

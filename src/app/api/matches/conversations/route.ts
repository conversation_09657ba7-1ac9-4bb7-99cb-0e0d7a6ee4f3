import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJWT } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Get user's matches with conversation data
    const matches = await prisma.match.findMany({
      where: {
        OR: [
          { user1Id: payload.userId },
          { user2Id: payload.userId }
        ],
        status: 'accepted'
      },
      include: {
        user1: {
          include: {
            profile: {
              include: {
                profilePictures: {
                  where: { isPrimary: true },
                  take: 1
                },
                town: true
              }
            }
          }
        },
        user2: {
          include: {
            profile: {
              include: {
                profilePictures: {
                  where: { isPrimary: true },
                  take: 1
                },
                town: true
              }
            }
          }
        },
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 1
        },
        _count: {
          select: { messages: true }
        }
      },
      orderBy: { matchedAt: 'desc' },
      skip,
      take: limit
    });

    // Transform data to include conversation partner info
    const conversations = matches.map((match: any) => {
      const partner = match.user1Id === payload.userId ? match.user2 : match.user1;
      const lastMessage = match.messages[0];
      
      return {
        matchId: match.id,
        partner: {
          id: partner.id,
          name: partner.profile?.fullName,
          profilePicture: partner.profile?.profilePictures[0]?.url,
          town: partner.profile?.town?.name,
          isOnline: partner.profile?.isOnline || false
        },
        lastMessage: lastMessage ? {
          content: lastMessage.content,
          senderId: lastMessage.senderId,
          createdAt: lastMessage.createdAt,
          isRead: lastMessage.isRead
        } : null,
        messageCount: match._count.messages,
        matchedAt: match.matchedAt
      };
    });

    return NextResponse.json({ 
      success: true, 
      data: conversations,
      pagination: {
        page,
        limit,
        total: conversations.length
      }
    });

  } catch (error) {
    console.error('Get conversations error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJWT } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const profile = await prisma.profile.findUnique({
      where: { userId: payload.userId },
      include: { profilePictures: true }
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Check if user has reached the limit of 6 pictures
    if (profile.profilePictures.length >= 6) {
      return NextResponse.json({ error: 'Maximum 6 profile pictures allowed' }, { status: 400 });
    }

    const formData = await request.formData();
    const file = formData.get('image') as File;
    const isPrimary = formData.get('isPrimary') === 'true';

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Validate file type and size
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed' }, { status: 400 });
    }

    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size too large. Maximum 5MB allowed' }, { status: 400 });
    }

    // TODO: Upload to cloud storage (Cloudinary, AWS S3, etc.)
    // For now, we'll simulate the upload
    const imageUrl = `https://example.com/uploads/${Date.now()}-${file.name}`;

    // If this is the primary image, unset other primary images
    if (isPrimary) {
      await prisma.profilePicture.updateMany({
        where: { profileId: profile.id, isPrimary: true },
        data: { isPrimary: false }
      });
    }

    // Create the profile picture record
    const profilePicture = await prisma.profilePicture.create({
      data: {
        profileId: profile.id,
        url: imageUrl,
        isPrimary: isPrimary || profile.profilePictures.length === 0, // First image is primary by default
        isApproved: false, // Requires moderation
        fileSize: file.size,
        dimensions: { width: 800, height: 600 } // Placeholder dimensions
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        id: profilePicture.id,
        url: profilePicture.url,
        isPrimary: profilePicture.isPrimary,
        isApproved: profilePicture.isApproved
      }
    });

  } catch (error) {
    console.error('Upload profile picture error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const pictureId = searchParams.get('id');

    if (!pictureId) {
      return NextResponse.json({ error: 'Picture ID is required' }, { status: 400 });
    }

    // Get user profile
    const profile = await prisma.profile.findUnique({
      where: { userId: payload.userId }
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Delete the profile picture
    await prisma.profilePicture.delete({
      where: {
        id: pictureId,
        profileId: profile.id
      }
    });

    return NextResponse.json({ success: true, message: 'Profile picture deleted successfully' });

  } catch (error) {
    console.error('Delete profile picture error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
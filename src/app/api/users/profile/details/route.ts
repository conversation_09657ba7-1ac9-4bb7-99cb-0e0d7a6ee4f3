import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJWT } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const profile = await prisma.profile.findUnique({
      where: { userId: payload.userId },
      include: {
        town: true,
        profilePictures: {
          where: { isApproved: true },
          orderBy: { isPrimary: 'desc' }
        }
      }
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Transform data to match the expected format
    const profileData = {
      fullName: profile.fullName,
      gender: profile.gender,
      dateOfBirth: profile.dateOfBirth.toISOString().split('T')[0],
      town: profile.townId,
      tribe: profile.tribe,
      religion: profile.religion,
      occupation: profile.occupation,
      education: profile.education,
      relationshipGoal: profile.relationshipGoal,
      bio: profile.bio,
      profilePictures: profile.profilePictures.map((pic: any) => ({
        url: pic.url,
        isPrimary: pic.isPrimary
      }))
    };

    return NextResponse.json({ success: true, data: profileData });
  } catch (error) {
    console.error('Get profile details error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      fullName,
      gender,
      dateOfBirth,
      town,
      tribe,
      religion,
      occupation,
      education,
      relationshipGoal,
      bio
    } = body;

    // Validate required fields
    if (!fullName || !gender || !dateOfBirth || !town) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Update profile
    const updatedProfile = await prisma.profile.update({
      where: { userId: payload.userId },
      data: {
        fullName,
        gender,
        dateOfBirth: new Date(dateOfBirth),
        townId: town,
        tribe,
        religion,
        occupation,
        education,
        relationshipGoal,
        bio
      }
    });

    return NextResponse.json({ success: true, data: updatedProfile });
  } catch (error) {
    console.error('Update profile error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJWT } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: { profile: true }
    });

    if (!user || !user.phone) {
      return NextResponse.json({ error: 'Phone number not found' }, { status: 404 });
    }

    // TODO: Implement actual SMS sending logic using M-Pesa or other SMS service
    // For now, just return success
    console.log(`SMS verification sent to ${user.phone}`);

    return NextResponse.json({ 
      success: true, 
      message: 'SMS verification sent successfully'
    });
  } catch (error) {
    console.error('Resend SMS verification error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJWT } from '@/lib/auth';

export async function PUT(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const token = authHeader.substring(7);
    const payload = verifyJWT(token);
    if (!payload?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { phone } = body;

    if (!phone) {
      return NextResponse.json({ error: 'Phone number is required' }, { status: 400 });
    }

    // Check if phone is already taken by another user
    const existingUser = await prisma.user.findFirst({
      where: {
        phone,
        id: { not: payload.userId }
      }
    });

    if (existingUser) {
      return NextResponse.json({ error: 'Phone number is already taken' }, { status: 400 });
    }

    // Update user phone
    const updatedUser = await prisma.user.update({
      where: { id: payload.userId },
      data: { phone }
    });

    return NextResponse.json({ 
      success: true, 
      data: { phone: updatedUser.phone },
      message: 'Phone number updated successfully. Please check your phone for verification.'
    });
  } catch (error) {
    console.error('Update phone error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 
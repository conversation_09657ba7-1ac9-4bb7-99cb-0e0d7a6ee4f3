'use client'

import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { Filter, Search, Heart, MapPin, Calendar, Users } from 'lucide-react'
import AdvancedSearch from '@/components/AdvancedSearch'
import PaymentPrompt from '@/components/PaymentPrompt'
import { checkUserSubscriptionStatus } from '@/lib/auth'

interface MatchProfile {
  id: string
  profile: {
    fullName: string
    dateOfBirth: string
    town: { name: string }
    tribe?: string
    profilePictures: { url: string; isPrimary: boolean }[]
  }
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<MatchProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)
  const [searchFilters, setSearchFilters] = useState<any>({})
  const [showPaymentPrompt, setShowPaymentPrompt] = useState(false)
  const [subscriptionStatus, setSubscriptionStatus] = useState<{
    isActive: boolean
    user?: any
    checked: boolean
  }>({ isActive: false, checked: false })

  useEffect(() => {
    checkSubscriptionAndFetchMatches()
  }, [searchFilters])

  const checkSubscriptionAndFetchMatches = async () => {
    setLoading(true)
    setError('')

    try {
      // First check subscription status
      const subscriptionResult = await checkUserSubscriptionStatus()
      setSubscriptionStatus({
        isActive: subscriptionResult.isActive,
        user: subscriptionResult.user,
        checked: true
      })

      // Always fetch matches regardless of subscription status
      // We'll control the matching action instead
      await fetchMatches()
    } catch (err) {
      console.error('Error checking subscription:', err)
      setError('Failed to load matches')
      setLoading(false)
    }
  }

  const fetchMatches = async (filters?: any) => {
    setLoading(true)
    setError('')
    try {
      const token = localStorage.getItem('kenyamatch_token')

      // Use advanced search if filters are provided
      const endpoint = filters && Object.keys(filters).length > 0
        ? '/api/matches/search'
        : '/api/matches'

      const options: RequestInit = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }

      if (filters && Object.keys(filters).length > 0) {
        options.method = 'POST'
        options.body = JSON.stringify(filters)
      }

      const res = await fetch(endpoint, options)
      const data = await res.json()

      if (data.success) {
        setMatches(data.data)
        if (filters) {
          toast.success(`Found ${data.data.length} matches`)
        }
      } else {
        setError(data.error || 'Failed to load matches')
        toast.error(data.error || 'Failed to load matches')
      }
    } catch (err) {
      setError('Failed to load matches')
      toast.error('Failed to load matches')
    } finally {
      setLoading(false)
    }
  }

  const handleAdvancedSearch = async (filters: any) => {
    setSearchFilters(filters)
    await fetchMatches(filters)
  }

  const handleMatch = async (targetUserId: string) => {
    // Check subscription status before allowing match
    if (!subscriptionStatus.isActive) {
      setShowPaymentPrompt(true)
      return
    }

    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/matches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ targetUserId })
      })
      const data = await res.json()
      if (data.success) {
        toast.success('Match request sent!')
      } else {
        // Check if it's a subscription error (backup check)
        if (res.status === 403 && (data.requiresPayment || data.error?.includes('subscription'))) {
          setShowPaymentPrompt(true)
          return
        }
        toast.error(data.error || 'Could not send match request')
      }
    } catch (err) {
      toast.error('Could not send match request')
    }
  }

  if (loading) return <div className="text-center py-12">Loading matches...</div>
  if (error) return <div className="text-center text-red-500 py-12">{error}</div>

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header with Search */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4 sm:mb-0">Discover Your Matches</h1>
        <div className="flex gap-3">
          <button
            onClick={() => setShowAdvancedSearch(true)}
            className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Filter className="w-4 h-4" />
            Advanced Search
          </button>
          {Object.keys(searchFilters).length > 0 && (
            <button
              onClick={() => {
                setSearchFilters({})
                fetchMatches()
              }}
              className="flex items-center gap-2 px-4 py-2 bg-pink-100 text-pink-700 rounded-lg hover:bg-pink-200 transition-colors"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>
      {/* Matches Grid */}
      {matches.length === 0 ? (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No matches found</h3>
          <p className="text-gray-500 mb-6">
            {Object.keys(searchFilters).length > 0
              ? 'Try adjusting your search filters to find more matches.'
              : 'Check back later for new potential matches!'
            }
          </p>
          {Object.keys(searchFilters).length > 0 && (
            <button
              onClick={() => {
                setSearchFilters({})
                fetchMatches()
              }}
              className="text-pink-600 hover:text-pink-700 font-medium"
            >
              Clear filters and see all matches
            </button>
          )}
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {matches.map((user) => (
            <div key={user.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6">
              {/* Profile Picture */}
              <div className="w-24 h-24 rounded-full overflow-hidden mb-4 bg-gray-200 flex items-center justify-center mx-auto">
                {user.profile.profilePictures[0]?.url ? (
                  <Image
                    src={user.profile.profilePictures[0].url}
                    alt={user.profile.fullName}
                    width={96}
                    height={96}
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <span className="text-3xl text-gray-500 font-bold">
                    {user.profile.fullName.charAt(0)}
                  </span>
                )}
              </div>

              {/* Profile Info */}
              <div className="text-center">
                <h3 className="font-semibold text-lg text-gray-800 mb-2">{user.profile.fullName}</h3>

                <div className="flex items-center justify-center gap-1 text-gray-600 text-sm mb-2">
                  <MapPin className="w-4 h-4" />
                  <span>{user.profile.town.name}</span>
                </div>

                {user.profile.tribe && (
                  <p className="text-gray-500 text-sm mb-2">{user.profile.tribe}</p>
                )}

                <div className="flex items-center justify-center gap-1 text-gray-500 text-sm mb-4">
                  <Calendar className="w-4 h-4" />
                  <span>
                    {new Date().getFullYear() - new Date(user.profile.dateOfBirth).getFullYear()} years old
                  </span>
                </div>

                <button
                  onClick={() => handleMatch(user.id)}
                  className="w-full bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  <Heart className="w-4 h-4" />
                  Send Match Request
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Advanced Search Modal */}
      <AdvancedSearch
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
      />

      {/* Payment Prompt Modal */}
      <PaymentPrompt
        isOpen={showPaymentPrompt}
        onClose={() => setShowPaymentPrompt(false)}
        feature="matches"
      />
    </div>
  )
}
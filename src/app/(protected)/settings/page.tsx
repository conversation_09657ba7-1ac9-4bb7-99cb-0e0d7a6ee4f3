'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Settings, User, Filter, Shield, Crown, Loader2, AlertCircle, CheckCircle } from 'lucide-react'
import toast from 'react-hot-toast'
import ProfileSection from '@/components/settings/ProfileSection'
import PreferencesSection from '@/components/settings/PreferencesSection'
import SecuritySection from '@/components/settings/SecuritySection'
import SubscriptionSection from '@/components/settings/SubscriptionSection'
import { isSubscriptionExpiringSoon, formatCurrency } from '@/lib/utils'

interface UserData {
  id: string
  email: string
  phone?: string
  isPaid: boolean
  subscriptionExpiresAt?: string
  emailVerified: boolean
  phoneVerified: boolean
}

interface ProfileData {
  fullName: string
  gender: 'MALE' | 'FEMALE' | 'OTHER'
  dateOfBirth: string
  town: string
  tribe?: string
  religion?: string
  occupation?: string
  education?: string
  relationshipGoal?: 'marriage' | 'serious' | 'casual' | 'friendship'
  bio?: string
  profilePictures?: Array<{ url: string; isPrimary: boolean }>
}

interface PreferencesData {
  genderPreference: 'MALE' | 'FEMALE' | 'OTHER'
  preferredAgeMin: number
  preferredAgeMax: number
  hasChildren: 'YES' | 'NO' | 'DOESNT_MATTER'
  maxDistance: number
  tribePreference?: string
  religionPreference?: string
}

interface Town {
  id: string
  name: string
  county: string
}

type SettingsTab = 'profile' | 'preferences' | 'security' | 'subscription'

export default function SettingsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState<SettingsTab>('profile')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [isDevMode, setIsDevMode] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  
  // Data states
  const [userData, setUserData] = useState<UserData | null>(null)
  const [profileData, setProfileData] = useState<ProfileData | null>(null)
  const [preferencesData, setPreferencesData] = useState<PreferencesData | null>(null)
  const [towns, setTowns] = useState<Town[]>([])

  useEffect(() => {
    // Set active tab from URL parameter
    const tabParam = searchParams.get('tab') as SettingsTab
    if (tabParam && ['profile', 'preferences', 'security', 'subscription'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [searchParams])

  useEffect(() => {
    fetchInitialData()
  }, [])

  const fetchInitialData = async () => {
    setIsLoading(true)
    setError('')
    
    try {
      const token = localStorage.getItem('kenyamatch_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const headers = { Authorization: `Bearer ${token}` }
      
      // Fetch all data in parallel with error handling for each request
      const fetchWithFallback = async (url: string, fallbackData: any) => {
        try {
          const response = await fetch(url, { headers })
          if (!response.ok) {
            console.warn(`Failed to fetch ${url}:`, response.status)
            return fallbackData
          }
          const data = await response.json()
          return data.success ? data.data : fallbackData
        } catch (error) {
          console.warn(`Error fetching ${url}:`, error)
          return fallbackData
        }
      }

      // Import demo data for fallbacks
      const { demoTowns, demoProfileData, demoPreferencesData, demoUserData } = await import('@/lib/demo-data')

      const [userData, profileData, preferencesData, townsData] = await Promise.all([
        fetchWithFallback('/api/users/profile', demoUserData),
        fetchWithFallback('/api/users/profile/details', demoProfileData),
        fetchWithFallback('/api/users/preferences', demoPreferencesData),
        fetchWithFallback('/api/towns', demoTowns)
      ])

      setUserData(userData)
      setProfileData(profileData)
      setPreferencesData(preferencesData)
      setTowns(townsData)

      // Check if we're in dev mode (using demo data)
      if (userData.id === 'user-123') {
        setIsDevMode(true)
      }

    } catch (err) {
      console.error('Failed to fetch settings data:', err)
      setError('Failed to load settings. Please try again.')
      setRetryCount(prev => prev + 1)
    } finally {
      setIsLoading(false)
    }
  }

  // Profile handlers
  const handleProfileSave = async (data: any) => {
    setIsSaving(true)
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/users/profile/details', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
      }
      
      const result = await res.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile')
      }
      
      setProfileData(prev => ({ ...prev, ...data }))
      toast.success('Profile updated successfully!')
      
      // If this was a profile completion redirect, suggest going to matches
      if (searchParams.get('tab') === 'profile') {
        toast.success('Profile completed! You can now browse matches.')
        // Show a follow-up toast with navigation suggestion
        setTimeout(() => {
          toast.success('Ready to find matches? Navigate to the Matches tab!')
        }, 2000)
      }
    } catch (error) {
      console.error('Profile save error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update profile')
      throw error
    } finally {
      setIsSaving(false)
    }
  }

  // Preferences handlers
  const handlePreferencesSave = async (data: any) => {
    setIsSaving(true)
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/users/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
      }
      
      const result = await res.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to update preferences')
      }
      
      setPreferencesData(prev => ({ ...prev, ...data }))
      toast.success('Preferences updated successfully!')
    } catch (error) {
      console.error('Preferences save error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update preferences')
      throw error
    } finally {
      setIsSaving(false)
    }
  }

  // Security handlers
  const handleEmailUpdate = async (data: { email: string }) => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch('/api/users/email', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(data)
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to update email')
    }
    
    setUserData(prev => prev ? { ...prev, email: data.email, emailVerified: false } : null)
  }

  const handlePhoneUpdate = async (data: { phone: string }) => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch('/api/users/phone', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(data)
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to update phone')
    }
    
    setUserData(prev => prev ? { ...prev, phone: data.phone, phoneVerified: false } : null)
  }

  const handlePasswordChange = async (data: any) => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch('/api/users/password', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(data)
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to change password')
    }
  }

  const handleResendVerification = async (type: 'email' | 'phone') => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch(`/api/users/verify/${type}/resend`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || `Failed to resend ${type} verification`)
    }
  }

  // Subscription handlers
  const handleRenewSubscription = async (method: 'STRIPE' | 'MPESA') => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch('/api/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ method })
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to initiate payment')
    }
    
    if (method === 'STRIPE' && result.data.checkoutUrl) {
      window.location.href = result.data.checkoutUrl
    }
  }

  const handleCancelSubscription = async () => {
    const token = localStorage.getItem('kenyamatch_token')
    const res = await fetch('/api/subscriptions/cancel', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
    
    if (!res.ok) {
      const errorData = await res.json()
      throw new Error(errorData.error || `HTTP error! status: ${res.status}`)
    }
    
    const result = await res.json()
    if (!result.success) {
      throw new Error(result.error || 'Failed to cancel subscription')
    }
    
    setUserData(prev => prev ? { ...prev, isPaid: false } : null)
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'preferences', label: 'Preferences', icon: Filter },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'subscription', label: 'Subscription', icon: Crown },
  ]

  // Subscription status
  const subscriptionStatus = userData ? {
    isActive: userData.isPaid && userData.subscriptionExpiresAt && new Date() < new Date(userData.subscriptionExpiresAt),
    isExpiringSoon: userData.subscriptionExpiresAt ? isSubscriptionExpiringSoon(userData.subscriptionExpiresAt) : false,
    expiresAt: userData.subscriptionExpiresAt
  } : null

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-pink-500 mx-auto mb-4" />
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  if (error && retryCount >= 3) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <AlertCircle className="w-12 h-12 mx-auto" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Settings</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="space-y-2">
            <button
              onClick={fetchInitialData}
              className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-lg font-medium"
            >
              Try Again
            </button>
            <button
              onClick={() => window.location.href = '/login'}
              className="block w-full border border-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50"
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
              <p className="text-gray-600">Manage your account, preferences, and subscription</p>
            </div>
            <div className="flex items-center gap-3">
              {subscriptionStatus && (
                <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                  subscriptionStatus.isActive 
                    ? subscriptionStatus.isExpiringSoon
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {subscriptionStatus.isActive ? (
                    <>
                      {subscriptionStatus.isExpiringSoon ? (
                        <AlertCircle className="w-4 h-4" />
                      ) : (
                        <CheckCircle className="w-4 h-4" />
                      )}
                      {subscriptionStatus.isExpiringSoon ? 'Expiring Soon' : 'Active'}
                    </>
                  ) : (
                    <>
                      <AlertCircle className="w-4 h-4" />
                      Inactive
                    </>
                  )}
                </div>
              )}
              {isDevMode && (
                <div className="bg-yellow-100 border border-yellow-300 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                  Demo Mode
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Tab Navigation */}
        <div className="lg:hidden mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
            <div className="grid grid-cols-4 gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as SettingsTab)}
                    className={`flex flex-col items-center py-3 px-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-pink-500 text-white'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5 mb-1" />
                    <span className="text-xs">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        <div className="lg:flex lg:gap-8">
          {/* Desktop Sidebar */}
          <div className="hidden lg:block lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sticky top-8">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as SettingsTab)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-pink-50 text-pink-700 border border-pink-200'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      {tab.label}
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 lg:p-8">
                {activeTab === 'profile' && (
                  <ProfileSection
                    initialData={profileData || undefined}
                    towns={towns}
                    onSave={handleProfileSave}
                  />
                )}

                {activeTab === 'preferences' && (
                  <PreferencesSection
                    initialData={preferencesData || undefined}
                    onSave={handlePreferencesSave}
                  />
                )}

                {activeTab === 'security' && (
                  <SecuritySection
                    initialData={userData ? {
                      email: userData.email,
                      phone: userData.phone,
                      emailVerified: userData.emailVerified,
                      phoneVerified: userData.phoneVerified,
                    } : undefined}
                    onUpdateEmail={handleEmailUpdate}
                    onUpdatePhone={handlePhoneUpdate}
                    onChangePassword={handlePasswordChange}
                    onResendVerification={handleResendVerification}
                  />
                )}

                {activeTab === 'subscription' && (
                  <SubscriptionSection
                    subscription={userData ? {
                      isPaid: userData.isPaid,
                      subscriptionExpiresAt: userData.subscriptionExpiresAt,
                      planName: 'Premium',
                      price: 1200,
                      duration: 120,
                    } : undefined}
                    onRenewSubscription={handleRenewSubscription}
                    onCancelSubscription={handleCancelSubscription}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 
'use client'

import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { User2, MapPin, Landmark, Briefcase, BookText } from 'lucide-react'

interface Profile {
  fullName: string
  dateOfBirth: string
  town: { name: string }
  tribe?: string
  religion?: string
  occupation?: string
  bio?: string
  profilePictures: { url: string; isPrimary: boolean }[]
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [editing, setEditing] = useState(false)
  const [form, setForm] = useState<any>({})

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch('/api/users/profile', {
          headers: { Authorization: `<PERSON><PERSON> ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setProfile(data.data)
          setForm(data.data)
        } else {
          setError(data.error || 'Failed to load profile')
        }
      } catch (err) {
        setError('Failed to load profile')
      } finally {
        setLoading(false)
      }
    }
    fetchProfile()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setForm((prev: any) => ({ ...prev, [name]: value }))
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(form)
      })
      const data = await res.json()
      if (data.success) {
        toast.success('Profile updated!')
        setProfile(data.data)
        setEditing(false)
      } else {
        toast.error(data.error || 'Could not update profile')
      }
    } catch (err) {
      toast.error('Could not update profile')
    }
  }

  if (loading) return <div className="text-center py-12">Loading profile...</div>
  if (error) return <div className="text-center text-red-500 py-12">{error}</div>
  if (!profile) return null

  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-gradient-to-br from-pink-50 to-white py-8">
      <div className="w-full max-w-lg bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center">
        <div className="relative mb-4">
          <div className="w-32 h-32 rounded-full border-4 border-pink-200 bg-gray-100 flex items-center justify-center overflow-hidden">
            {profile.profilePictures?.[0]?.url ? (
              <Image
                src={profile.profilePictures[0].url}
                alt={profile.fullName || 'User profile picture'}
                width={128}
                height={128}
                className="object-cover w-full h-full"
              />
            ) : (
              <span className="text-5xl text-pink-400 font-bold">
                {profile.fullName?.[0] || <User2 className="w-12 h-12" />}
              </span>
            )}
          </div>
        </div>
        <div className="text-2xl font-bold text-gray-800 mb-1 text-center">
          {profile.fullName || <span className="text-gray-400">Your Name</span>}
        </div>
        <div className="flex items-center gap-2 text-gray-500 mb-4 text-center">
          <MapPin className="w-4 h-4" />
          <span>{profile.town?.name || <span className="text-gray-400">Location not set</span>}</span>
        </div>

        {/* About Me Section */}
        <div className="w-full mb-6">
          <h3 className="font-semibold text-pink-600 mb-1 text-left">About Me</h3>
          <div className="bg-pink-50 rounded-lg p-3 text-gray-700 text-center min-h-[48px]">
            {profile.bio ? (
              <span>{profile.bio}</span>
            ) : (
              <span className="text-gray-400">Tell us about yourself! Add a short bio to let others know you better.</span>
            )}
          </div>
        </div>

        {/* Basic Info Section */}
        <div className="w-full mb-4">
          <h3 className="font-semibold text-pink-600 mb-1 text-left">Basic Info</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div className="flex items-center gap-2 text-gray-700">
              <span className="font-medium">Date of Birth:</span>
              <span>{profile.dateOfBirth || <span className="text-gray-400">Not set</span>}</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700">
              <span className="font-medium">Location:</span>
              <span>{profile.town?.name || <span className="text-gray-400">Not set</span>}</span>
            </div>
          </div>
        </div>

        {/* Cultural Background Section */}
        <div className="w-full mb-4">
          <h3 className="font-semibold text-pink-600 mb-1 text-left">Cultural Background</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div className="flex items-center gap-2 text-gray-700">
              <Landmark className="w-4 h-4 text-pink-400" />
              <span>Ethnic Group:</span>
              <span className="font-medium">{profile.tribe || <span className="text-gray-400">Not set</span>}</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700">
              <BookText className="w-4 h-4 text-pink-400" />
              <span>Religion:</span>
              <span className="font-medium">{profile.religion || <span className="text-gray-400">Not set</span>}</span>
            </div>
          </div>
        </div>

        {/* Professional Details Section */}
        <div className="w-full mb-6">
          <h3 className="font-semibold text-pink-600 mb-1 text-left">Professional Details</h3>
          <div className="flex items-center gap-2 text-gray-700">
            <Briefcase className="w-4 h-4 text-pink-400" />
            <span>Profession:</span>
            <span className="font-medium">{profile.occupation || <span className="text-gray-400">Add your profession</span>}</span>
          </div>
        </div>

        <button
          className="mt-2 bg-pink-500 hover:bg-pink-600 text-white px-8 py-2 rounded-full font-semibold shadow transition"
          onClick={() => setEditing(true)}
        >
          {profile.fullName ? 'Update Profile' : 'Complete Your Profile'}
        </button>
      </div>
    </div>
  )
} 
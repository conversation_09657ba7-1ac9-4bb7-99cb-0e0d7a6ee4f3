'use client'

import { useEffect, useState, useRef } from 'react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { User2, MessageCircle } from 'lucide-react'

interface Match {
  id: string
  user1: { id: string; profile: { fullName: string; profilePictures: { url: string }[] } }
  user2: { id: string; profile: { fullName: string; profilePictures: { url: string }[] } }
}

interface Message {
  id: string
  senderId: string
  content: string
  createdAt: string
  sender: { profile: { fullName: string; profilePictures: { url: string }[] } }
}

export default function MessagesPage() {
  const [matches, setMatches] = useState<Match[]>([])
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loadingMatches, setLoadingMatches] = useState(true)
  const [loadingMessages, setLoadingMessages] = useState(false)
  const [error, setError] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const fetchMatches = async () => {
      setLoadingMatches(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch('/api/matches/conversations', {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setMatches(data.data)
        } else {
          setError(data.error || 'Failed to load conversations')
        }
      } catch (err) {
        setError('Failed to load conversations')
      } finally {
        setLoadingMatches(false)
      }
    }
    fetchMatches()
  }, [])

  useEffect(() => {
    if (!selectedMatch) return
    let polling: NodeJS.Timeout
    const fetchMessages = async () => {
      setLoadingMessages(true)
      setError('')
      try {
        const token = localStorage.getItem('kenyamatch_token')
        const res = await fetch(`/api/messages?matchId=${selectedMatch.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await res.json()
        if (data.success) {
          setMessages(data.data)
        } else {
          setError(data.error || 'Failed to load messages')
        }
      } catch (err) {
        setError('Failed to load messages')
      } finally {
        setLoadingMessages(false)
      }
    }
    fetchMessages()
    polling = setInterval(fetchMessages, 3000)
    return () => clearInterval(polling)
  }, [selectedMatch])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || !selectedMatch) return
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const res = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ matchId: selectedMatch.id, content: newMessage })
      })
      const data = await res.json()
      if (data.success) {
        setNewMessage('')
        setMessages((prev) => [...prev, data.data])
      } else {
        toast.error(data.error || 'Could not send message')
      }
    } catch (err) {
      toast.error('Could not send message')
    }
  }

  return (
    <div className="flex h-[80vh] bg-white rounded-xl shadow overflow-hidden border">
      {/* Sidebar: Matches */}
      <div className="w-72 border-r bg-gray-50 p-4 overflow-y-auto flex flex-col">
        <h2 className="font-bold mb-4 text-gray-700 text-lg flex items-center gap-2"><MessageCircle className="w-5 h-5 text-pink-500" /> Conversations</h2>
        {loadingMatches ? (
          <div className="text-gray-400">Loading...</div>
        ) : error ? (
          <div className="text-red-500">{error}</div>
        ) : matches.length === 0 ? (
          <div className="text-gray-400">No conversations yet.</div>
        ) : (
          <ul className="space-y-1">
            {matches.map((match) => {
              const userId = localStorage.getItem('kenyamatch_userId')
              const other = match.user1.id === userId ? match.user2 : match.user1
              return (
                <li
                  key={match.id}
                  className={`flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors mb-1 select-none ${selectedMatch?.id === match.id ? 'bg-pink-100 border border-pink-300' : 'hover:bg-gray-100'}`}
                  onClick={() => setSelectedMatch(match)}
                >
                  {other.profile.profilePictures[0]?.url ? (
                    <Image
                      src={other.profile.profilePictures[0].url}
                      alt={other.profile.fullName}
                      width={40}
                      height={40}
                      className="rounded-full object-cover border"
                    />
                  ) : (
                    <span className="w-10 h-10 rounded-full bg-pink-200 flex items-center justify-center text-lg font-bold text-pink-700 border">
                      {other.profile.fullName[0]}
                    </span>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-800 truncate">{other.profile.fullName}</div>
                    {/* Optionally, show last message preview here */}
                  </div>
                </li>
              )
            })}
          </ul>
        )}
      </div>
      {/* Chat Area */}
      <div className="flex-1 flex flex-col bg-gradient-to-br from-pink-50 to-white">
        {selectedMatch ? (
          <>
            {/* Chat Header */}
            <div className="border-b px-6 py-4 bg-white flex items-center gap-3 shadow-sm">
              {(() => {
                const userId = localStorage.getItem('kenyamatch_userId')
                const other = selectedMatch.user1.id === userId ? selectedMatch.user2 : selectedMatch.user1
                return other.profile.profilePictures[0]?.url ? (
                  <Image
                    src={other.profile.profilePictures[0].url}
                    alt={other.profile.fullName}
                    width={40}
                    height={40}
                    className="rounded-full object-cover border"
                  />
                ) : (
                  <span className="w-10 h-10 rounded-full bg-pink-200 flex items-center justify-center text-lg font-bold text-pink-700 border">
                    {other.profile.fullName[0]}
                  </span>
                )
              })()}
              <span className="font-semibold text-gray-800 text-lg">
                {(() => {
                  const userId = localStorage.getItem('kenyamatch_userId')
                  const other = selectedMatch.user1.id === userId ? selectedMatch.user2 : selectedMatch.user1
                  return other.profile.fullName
                })()}
              </span>
            </div>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-transparent">
              {loadingMessages ? (
                <div className="text-gray-400">Loading messages...</div>
              ) : error ? (
                <div className="text-red-500">{error}</div>
              ) : messages.length === 0 ? (
                <div className="text-gray-400">No messages yet.</div>
              ) : (
                messages.map((msg) => (
                  <div key={msg.id} className={`flex ${msg.senderId === localStorage.getItem('kenyamatch_userId') ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs px-4 py-2 rounded-2xl shadow-sm ${msg.senderId === localStorage.getItem('kenyamatch_userId') ? 'bg-pink-500 text-white rounded-br-none' : 'bg-white text-gray-900 border rounded-bl-none'}`}>
                      <div className="text-base leading-snug">{msg.content}</div>
                      <div className="text-xs mt-1 opacity-60 text-right">{new Date(msg.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
            {/* Message Input */}
            <form onSubmit={handleSend} className="p-4 border-t bg-white flex gap-2 items-center">
              <input
                type="text"
                value={newMessage}
                onChange={e => setNewMessage(e.target.value)}
                className="flex-1 px-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:border-pink-400 bg-gray-50"
                placeholder="Type your message..."
              />
              <button
                type="submit"
                className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-full font-semibold shadow"
                disabled={!newMessage.trim()}
              >
                Send
              </button>
            </form>
          </>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center text-gray-400">
            <User2 className="w-16 h-16 mb-4 text-pink-200" />
            <div className="text-lg font-semibold">Select a conversation to start chatting.</div>
          </div>
        )}
      </div>
    </div>
  )
} 
import { cn } from '@/lib/utils'

interface SkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular'
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export default function Skeleton({ 
  className, 
  variant = 'rectangular', 
  size = 'md' 
}: SkeletonProps) {
  const baseClasses = "animate-pulse bg-gray-200 rounded"
  
  const sizeClasses = {
    sm: variant === 'text' ? 'h-3' : variant === 'circular' ? 'w-6 h-6' : 'h-4',
    md: variant === 'text' ? 'h-4' : variant === 'circular' ? 'w-8 h-8' : 'h-6',
    lg: variant === 'text' ? 'h-6' : variant === 'circular' ? 'w-12 h-12' : 'h-8',
    xl: variant === 'text' ? 'h-8' : variant === 'circular' ? 'w-16 h-16' : 'h-12',
  }

  const variantClasses = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg',
  }

  return (
    <div
      className={cn(
        baseClasses,
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    />
  )
}

// Predefined skeleton components for common use cases
export function SkeletonText({ lines = 1, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} variant="text" className="w-full" />
      ))}
    </div>
  )
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("bg-white rounded-lg border border-gray-200 p-6", className)}>
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Skeleton variant="circular" size="md" />
          <Skeleton variant="text" className="w-32" />
        </div>
        <SkeletonText lines={3} />
        <div className="flex gap-2">
          <Skeleton className="w-20 h-8" />
          <Skeleton className="w-20 h-8" />
        </div>
      </div>
    </div>
  )
}

export function SkeletonForm({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="space-y-4">
        <Skeleton variant="text" className="w-24" />
        <Skeleton className="w-full h-12" />
      </div>
      <div className="space-y-4">
        <Skeleton variant="text" className="w-32" />
        <Skeleton className="w-full h-12" />
      </div>
      <div className="space-y-4">
        <Skeleton variant="text" className="w-28" />
        <Skeleton className="w-full h-12" />
      </div>
      <div className="flex gap-3 pt-4">
        <Skeleton className="w-24 h-10" />
        <Skeleton className="w-24 h-10" />
      </div>
    </div>
  )
} 
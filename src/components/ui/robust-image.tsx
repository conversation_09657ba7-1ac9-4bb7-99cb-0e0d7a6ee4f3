'use client'

import Image from 'next/image'
import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { getImageFallbacks, optimizeImageUrl, isValidImageUrl } from '@/lib/image-utils'

interface RobustImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  fill?: boolean
  priority?: boolean
  sizes?: string
  fallbackSrc?: string
  onError?: () => void
}

/**
 * A robust image component that handles external images gracefully
 * with fallback support and error handling
 */
export function RobustImage({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  sizes,
  fallbackSrc,
  onError,
  ...props
}: RobustImageProps) {
  const [currentSrc, setCurrentSrc] = useState(src)
  const [fallbackIndex, setFallbackIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Generate fallback URLs
  const fallbacks = getImageFallbacks(
    src,
    alt,
    Math.max(width || 400, height || 400)
  )

  // Add custom fallback if provided
  if (fallbackSrc && !fallbacks.includes(fallbackSrc)) {
    fallbacks.splice(-1, 0, fallbackSrc)
  }

  useEffect(() => {
    // Reset when src changes
    setCurrentSrc(src)
    setFallbackIndex(0)
    setIsLoading(true)
  }, [src])

  const handleError = () => {
    const nextIndex = fallbackIndex + 1
    if (nextIndex < fallbacks.length) {
      setFallbackIndex(nextIndex)
      setCurrentSrc(fallbacks[nextIndex])
    }
    onError?.()
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  // Optimize the image URL if it's valid
  const optimizedSrc = isValidImageUrl(currentSrc)
    ? optimizeImageUrl(currentSrc, width, height)
    : currentSrc

  const imageProps = {
    src: optimizedSrc,
    alt,
    className: cn(
      className,
      isLoading && 'opacity-0 transition-opacity duration-300',
      !isLoading && 'opacity-100'
    ),
    onError: handleError,
    onLoad: handleLoad,
    priority,
    sizes,
    ...props,
  }

  if (fill) {
    return (
      <Image
        {...imageProps}
        fill
      />
    )
  }

  return (
    <Image
      {...imageProps}
      width={width || 400}
      height={height || 400}
    />
  )
}

/**
 * Avatar-specific robust image component
 */
export function RobustAvatar({
  src,
  alt,
  size = 40,
  className,
  ...props
}: Omit<RobustImageProps, 'width' | 'height'> & { size?: number }) {
  return (
    <div className={cn('relative overflow-hidden rounded-full', className)} style={{ width: size, height: size }}>
      <RobustImage
        src={src}
        alt={alt}
        width={size}
        height={size}
        className="object-cover"
        fill
        {...props}
      />
    </div>
  )
}

/**
 * Profile picture component with multiple fallback options
 */
export function RobustProfilePicture({
  src,
  alt,
  size = 200,
  className,
  ...props
}: Omit<RobustImageProps, 'width' | 'height'> & { size?: number }) {
  return (
    <div className={cn('relative overflow-hidden rounded-lg', className)} style={{ width: size, height: size }}>
      <RobustImage
        src={src}
        alt={alt}
        width={size}
        height={size}
        className="object-cover"
        fill
        {...props}
      />
    </div>
  )
}

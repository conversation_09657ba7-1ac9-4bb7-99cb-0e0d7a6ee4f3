'use client'

import Image from 'next/image'
import { useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { getImageFallbacks, optimizeImageUrl, isValidImageUrl } from '@/lib/image-utils'
import { ImageErrorBoundary } from './image-error-boundary'
import { Loader2 } from 'lucide-react'

interface SafeImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  fill?: boolean
  priority?: boolean
  sizes?: string
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
  showLoader?: boolean
  loaderClassName?: string
  errorFallback?: React.ReactNode
  maxRetries?: number
}

/**
 * A comprehensive safe image component that handles all edge cases
 * - Multiple fallback URLs
 * - Loading states
 * - Error boundaries
 * - Retry mechanisms
 * - URL optimization
 * - Security validation
 */
export function SafeImage({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  sizes,
  quality = 80,
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  onError,
  showLoader = true,
  loaderClassName,
  errorFallback,
  maxRetries = 3,
  ...props
}: SafeImageProps) {
  const [currentSrc, setCurrentSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [fallbackIndex, setFallbackIndex] = useState(0)

  // Generate fallback URLs
  const fallbacks = getImageFallbacks(src, alt, Math.max(width || 400, height || 400))

  // Reset state when src changes
  useEffect(() => {
    setCurrentSrc(src)
    setIsLoading(true)
    setHasError(false)
    setRetryCount(0)
    setFallbackIndex(0)
  }, [src])

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    setHasError(false)
    onLoad?.()
  }, [onLoad])

  const handleError = useCallback(() => {
    setIsLoading(false)
    
    // Try next fallback
    const nextIndex = fallbackIndex + 1
    if (nextIndex < fallbacks.length) {
      setFallbackIndex(nextIndex)
      setCurrentSrc(fallbacks[nextIndex])
      setIsLoading(true)
      return
    }
    
    // All fallbacks exhausted, try retry if available
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1)
      setFallbackIndex(0)
      setCurrentSrc(src)
      setIsLoading(true)
      return
    }
    
    // Final error state
    setHasError(true)
    onError?.()
  }, [fallbackIndex, fallbacks, retryCount, maxRetries, src, onError])

  // Optimize the current source URL
  const optimizedSrc = isValidImageUrl(currentSrc) 
    ? optimizeImageUrl(currentSrc, width, height)
    : currentSrc

  // Error fallback component
  const ErrorFallback = () => (
    <div className={cn(
      'flex items-center justify-center bg-gray-100 border-2 border-dashed border-gray-300 rounded',
      fill ? 'absolute inset-0' : '',
      className
    )} style={!fill ? { width, height } : undefined}>
      {errorFallback || (
        <div className="text-center p-2">
          <div className="text-gray-400 text-xs">Failed to load image</div>
          <button
            onClick={() => {
              setHasError(false)
              setRetryCount(0)
              setFallbackIndex(0)
              setCurrentSrc(src)
              setIsLoading(true)
            }}
            className="text-xs text-blue-500 hover:text-blue-700 mt-1"
          >
            Retry
          </button>
        </div>
      )}
    </div>
  )

  // Loading component
  const LoadingComponent = () => (
    showLoader && isLoading ? (
      <div className={cn(
        'absolute inset-0 flex items-center justify-center bg-gray-50',
        loaderClassName
      )}>
        <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
      </div>
    ) : null
  )

  if (hasError) {
    return <ErrorFallback />
  }

  const imageProps = {
    src: optimizedSrc,
    alt,
    className: cn(
      'transition-opacity duration-300',
      isLoading ? 'opacity-0' : 'opacity-100',
      className
    ),
    onLoad: handleLoad,
    onError: handleError,
    priority,
    sizes,
    quality,
    placeholder,
    blurDataURL,
    ...props,
  }

  const ImageComponent = () => {
    if (fill) {
      return <Image {...imageProps} fill />
    }
    
    return (
      <Image
        {...imageProps}
        width={width || 400}
        height={height || 400}
      />
    )
  }

  return (
    <ImageErrorBoundary fallback={<ErrorFallback />}>
      <div className={cn('relative', fill ? 'w-full h-full' : '')} style={!fill ? { width, height } : undefined}>
        <ImageComponent />
        <LoadingComponent />
      </div>
    </ImageErrorBoundary>
  )
}

/**
 * Safe Avatar component
 */
export function SafeAvatar({
  src,
  alt,
  size = 40,
  className,
  ...props
}: Omit<SafeImageProps, 'width' | 'height' | 'fill'> & { size?: number }) {
  return (
    <div className={cn('relative rounded-full overflow-hidden', className)} style={{ width: size, height: size }}>
      <SafeImage
        src={src}
        alt={alt}
        fill
        className="object-cover"
        {...props}
      />
    </div>
  )
}

/**
 * Safe Profile Picture component
 */
export function SafeProfilePicture({
  src,
  alt,
  size = 200,
  className,
  ...props
}: Omit<SafeImageProps, 'width' | 'height' | 'fill'> & { size?: number }) {
  return (
    <div className={cn('relative rounded-lg overflow-hidden', className)} style={{ width: size, height: size }}>
      <SafeImage
        src={src}
        alt={alt}
        fill
        className="object-cover"
        {...props}
      />
    </div>
  )
}

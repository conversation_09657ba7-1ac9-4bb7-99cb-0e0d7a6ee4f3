'use client'

import React, { Component, ReactNode } from 'react'
import { AlertTriangle } from 'lucide-react'

interface ImageErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface ImageErrorBoundaryState {
  hasError: boolean
  error?: Error
}

/**
 * Error boundary specifically for image-related errors
 * Provides graceful fallback when image components fail
 */
export class ImageErrorBoundary extends Component<ImageErrorBoundaryProps, ImageErrorBoundaryState> {
  constructor(props: ImageErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ImageErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    console.error('Image Error Boundary caught an error:', error, errorInfo)
    
    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default fallback UI
      return (
        <div className="flex items-center justify-center w-full h-full min-h-[200px] bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center p-4">
            <AlertTriangle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">Image failed to load</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="mt-2 text-xs text-blue-500 hover:text-blue-700 underline"
            >
              Try again
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Hook version of the image error boundary for functional components
 */
export function useImageErrorHandler() {
  const [hasError, setHasError] = React.useState(false)
  const [retryCount, setRetryCount] = React.useState(0)

  const handleError = React.useCallback(() => {
    setHasError(true)
  }, [])

  const retry = React.useCallback(() => {
    setHasError(false)
    setRetryCount(prev => prev + 1)
  }, [])

  const reset = React.useCallback(() => {
    setHasError(false)
    setRetryCount(0)
  }, [])

  return {
    hasError,
    retryCount,
    handleError,
    retry,
    reset
  }
}

/**
 * Wrapper component that provides image error handling
 */
export function ImageWithErrorBoundary({ 
  children, 
  fallback,
  onError 
}: {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}) {
  return (
    <ImageErrorBoundary fallback={fallback} onError={onError}>
      {children}
    </ImageErrorBoundary>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, X, MapPin, Users, Heart, Star } from 'lucide-react'
import toast from 'react-hot-toast'

interface FilterOptions {
  towns: Array<{ id: string; name: string; county: string }>
  tribes: string[]
  religions: string[]
  ageRanges: Array<{ label: string; min: number; max: number }>
  genders: Array<{ value: string; label: string }>
  childrenOptions: Array<{ value: string; label: string }>
  distanceOptions: Array<{ value: number; label: string }>
}

interface SearchFilters {
  gender?: string
  minAge?: number
  maxAge?: number
  town?: string
  tribe?: string
  religion?: string
  hasChildren?: string
  verified?: boolean
  maxDistance?: number
}

interface AdvancedSearchProps {
  onSearch: (filters: SearchFilters) => void
  onClose: () => void
  isOpen: boolean
}

export default function AdvancedSearch({ onSearch, onClose, isOpen }: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({})
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen && !filterOptions) {
      fetchFilterOptions()
    }
  }, [isOpen, filterOptions])

  const fetchFilterOptions = async () => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch('/api/matches/filters', {
        headers: { Authorization: `Bearer ${token}` }
      })
      const data = await response.json()
      
      if (data.success) {
        setFilterOptions(data.data)
      } else {
        toast.error('Failed to load filter options')
      }
    } catch (error) {
      toast.error('Failed to load filter options')
    }
  }

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleAgeRangeChange = (range: { min: number; max: number }) => {
    setFilters(prev => ({
      ...prev,
      minAge: range.min,
      maxAge: range.max
    }))
  }

  const handleSearch = async () => {
    setLoading(true)
    try {
      await onSearch(filters)
      onClose()
    } catch (error) {
      toast.error('Search failed')
    } finally {
      setLoading(false)
    }
  }

  const clearFilters = () => {
    setFilters({})
  }

  const hasActiveFilters = Object.keys(filters).length > 0

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-pink-500" />
            <h2 className="text-xl font-semibold text-gray-900">Advanced Search</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {!filterOptions ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500 mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading filter options...</p>
            </div>
          ) : (
            <>
              {/* Gender */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Users className="w-4 h-4 inline mr-1" />
                  Gender
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {filterOptions.genders.map((gender) => (
                    <button
                      key={gender.value}
                      onClick={() => handleFilterChange('gender', gender.value)}
                      className={`p-3 text-sm rounded-lg border transition-colors ${
                        filters.gender === gender.value
                          ? 'bg-pink-50 border-pink-300 text-pink-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {gender.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Age Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Age Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {filterOptions.ageRanges.map((range) => (
                    <button
                      key={range.label}
                      onClick={() => handleAgeRangeChange(range)}
                      className={`p-3 text-sm rounded-lg border transition-colors ${
                        filters.minAge === range.min && filters.maxAge === range.max
                          ? 'bg-pink-50 border-pink-300 text-pink-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {range.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  Location
                </label>
                <select
                  value={filters.town || ''}
                  onChange={(e) => handleFilterChange('town', e.target.value || undefined)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">Any location</option>
                  {filterOptions.towns.map((town) => (
                    <option key={town.id} value={town.name}>
                      {town.name}, {town.county}
                    </option>
                  ))}
                </select>
              </div>

              {/* Distance */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Distance
                </label>
                <select
                  value={filters.maxDistance || ''}
                  onChange={(e) => handleFilterChange('maxDistance', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">Any distance</option>
                  {filterOptions.distanceOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tribe */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tribe
                </label>
                <select
                  value={filters.tribe || ''}
                  onChange={(e) => handleFilterChange('tribe', e.target.value || undefined)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">Any tribe</option>
                  {filterOptions.tribes.map((tribe) => (
                    <option key={tribe} value={tribe}>
                      {tribe}
                    </option>
                  ))}
                </select>
              </div>

              {/* Religion */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Religion
                </label>
                <select
                  value={filters.religion || ''}
                  onChange={(e) => handleFilterChange('religion', e.target.value || undefined)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">Any religion</option>
                  {filterOptions.religions.map((religion) => (
                    <option key={religion} value={religion}>
                      {religion}
                    </option>
                  ))}
                </select>
              </div>

              {/* Children */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Heart className="w-4 h-4 inline mr-1" />
                  Children
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {filterOptions.childrenOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleFilterChange('hasChildren', option.value)}
                      className={`p-3 text-sm rounded-lg border transition-colors ${
                        filters.hasChildren === option.value
                          ? 'bg-pink-50 border-pink-300 text-pink-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Verified Only */}
              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={filters.verified || false}
                    onChange={(e) => handleFilterChange('verified', e.target.checked || undefined)}
                    className="w-4 h-4 text-pink-600 border-gray-300 rounded focus:ring-pink-500"
                  />
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Verified users only
                  </span>
                </label>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={clearFilters}
            disabled={!hasActiveFilters}
            className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
          >
            Clear all filters
          </button>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSearch}
              disabled={loading || !filterOptions}
              className="px-6 py-2 text-sm font-medium text-white bg-pink-600 rounded-lg hover:bg-pink-700 disabled:opacity-50 flex items-center gap-2"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Search className="w-4 h-4" />
              )}
              Search
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

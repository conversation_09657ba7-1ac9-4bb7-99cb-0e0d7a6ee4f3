'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Crown, CreditCard, Smartphone, Heart, Users, MessageCircle, Star, ArrowRight, X } from 'lucide-react'

interface PaymentPromptProps {
  isOpen: boolean
  onClose: () => void
  feature?: string
}

export default function PaymentPrompt({ isOpen, onClose, feature = "matches" }: PaymentPromptProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  if (!isOpen) return null

  const handlePaymentRedirect = () => {
    setIsLoading(true)
    router.push('/upgrade')
  }

  const features = [
    {
      icon: <Users className="w-5 h-5 text-gray-600" />,
      title: "Unlimited Matches",
      description: "Connect with unlimited potential partners"
    },
    {
      icon: <MessageCircle className="w-5 h-5 text-gray-600" />,
      title: "Unlimited Messaging",
      description: "Chat freely with all your matches"
    },
    {
      icon: <Star className="w-5 h-5 text-gray-600" />,
      title: "Advanced Filters",
      description: "Find exactly who you're looking for"
    },
    {
      icon: <Heart className="w-5 h-5 text-gray-600" />,
      title: "Priority Support",
      description: "Get help when you need it most"
    }
  ]

  return (
    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-y-auto mx-auto border border-gray-200/50">
        {/* Header */}
        <div className="relative bg-white p-8 md:p-10">
          <button
            onClick={onClose}
            className="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-50 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>

          <div className="text-center max-w-md mx-auto">
            <div className="w-16 h-16 bg-gray-50 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Crown className="w-8 h-8 text-gray-700" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-3 tracking-tight">Unlock Premium Features</h2>
            <p className="text-gray-600 leading-relaxed">
              {feature === "matches"
                ? "To connect with matches, you need an active subscription"
                : `To access ${feature}, you need an active subscription`
              }
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="px-8 md:px-10 pb-8 md:pb-10">
          <div className="grid lg:grid-cols-5 gap-8 lg:gap-12">
            {/* Features Section */}
            <div className="lg:col-span-3 order-2 lg:order-1">
              <h3 className="text-lg font-medium text-gray-900 mb-8">What you'll get with Premium:</h3>
              <div className="grid md:grid-cols-2 gap-6">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-4 p-5 bg-gray-50/50 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0 w-11 h-11 bg-white rounded-xl flex items-center justify-center shadow-sm border border-gray-100">
                      {feature.icon}
                    </div>
                    <div className="pt-1">
                      <div className="font-medium text-gray-900 mb-2">{feature.title}</div>
                      <div className="text-sm text-gray-600 leading-relaxed">{feature.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing & Payment Section */}
            <div className="lg:col-span-2 order-1 lg:order-2">
              <div className="bg-gray-50/50 rounded-2xl p-8 border border-gray-100 lg:sticky lg:top-4">
                <div className="text-center mb-8">
                  <div className="text-4xl font-semibold text-gray-900 mb-3 tracking-tight">KSh 1,200</div>
                  <div className="text-gray-600 mb-4 text-lg">120 days of premium access</div>
                  <div className="inline-block bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium border border-gray-200">
                    Only KSh 10 per day
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="mb-8">
                  <h4 className="font-medium text-gray-900 mb-6 text-center">Choose Payment Method:</h4>
                  <div className="space-y-3">
                    <div className="flex items-center p-4 border border-gray-200 rounded-xl hover:border-gray-300 hover:bg-white transition-all cursor-pointer group">
                      <Smartphone className="w-5 h-5 text-gray-600 mr-4 group-hover:text-gray-700" />
                      <span className="font-medium text-gray-900">M-Pesa Payment</span>
                    </div>
                    <div className="flex items-center p-4 border border-gray-200 rounded-xl hover:border-gray-300 hover:bg-white transition-all cursor-pointer group">
                      <CreditCard className="w-5 h-5 text-gray-600 mr-4 group-hover:text-gray-700" />
                      <span className="font-medium text-gray-900">Credit/Debit Card</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                  <button
                    onClick={handlePaymentRedirect}
                    disabled={isLoading}
                    className="w-full bg-gray-900 text-white py-4 px-6 rounded-xl font-medium hover:bg-gray-800 transition-colors flex items-center justify-center gap-2 disabled:opacity-50 shadow-sm"
                  >
                    {isLoading ? (
                      'Redirecting...'
                    ) : (
                      <>
                        Get Premium Access
                        <ArrowRight className="w-4 h-4" />
                      </>
                    )}
                  </button>

                  <button
                    onClick={onClose}
                    className="w-full bg-white text-gray-700 py-3 px-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200"
                  >
                    Maybe Later
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-10 pt-8 border-t border-gray-100">
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium">Secure Payment</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium">Instant Activation</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="font-medium">Mobile Friendly</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

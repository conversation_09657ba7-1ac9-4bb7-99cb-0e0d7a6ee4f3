'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Shield, Mail, Phone, Lock, Eye, EyeOff, CheckCircle, AlertCircle } from 'lucide-react'
import toast from 'react-hot-toast'
import { cn, isValidEmail, isValidPhoneNumber, formatPhoneNumber } from '@/lib/utils'

const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

const phoneSchema = z.object({
  phone: z.string().refine((phone) => isValidPhoneNumber(phone), {
    message: 'Please enter a valid Kenyan phone number (e.g., +************ or **********)',
  }),
})

const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type EmailFormData = z.infer<typeof emailSchema>
type PhoneFormData = z.infer<typeof phoneSchema>
type PasswordFormData = z.infer<typeof passwordSchema>

interface SecuritySectionProps {
  initialData?: {
    email: string
    phone?: string
    emailVerified: boolean
    phoneVerified: boolean
  }
  onUpdateEmail: (data: EmailFormData) => Promise<void>
  onUpdatePhone: (data: PhoneFormData) => Promise<void>
  onChangePassword: (data: PasswordFormData) => Promise<void>
  onResendVerification: (type: 'email' | 'phone') => Promise<void>
}

export default function SecuritySection({ 
  initialData, 
  onUpdateEmail, 
  onUpdatePhone, 
  onChangePassword,
  onResendVerification 
}: SecuritySectionProps) {
  const [activeTab, setActiveTab] = useState<'email' | 'phone' | 'password'>('email')
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const emailForm = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: initialData?.email || '',
    },
  })

  const phoneForm = useForm<PhoneFormData>({
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      phone: initialData?.phone || '',
    },
  })

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  })

  const handleEmailSubmit = async (data: EmailFormData) => {
    setIsLoading(true)
    try {
      await onUpdateEmail(data)
      toast.success('Email updated successfully! Please check your email for verification.')
    } catch (error) {
      toast.error('Failed to update email')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePhoneSubmit = async (data: PhoneFormData) => {
    setIsLoading(true)
    try {
      await onUpdatePhone(data)
      toast.success('Phone number updated successfully! Please check your phone for verification.')
    } catch (error) {
      toast.error('Failed to update phone number')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    setIsLoading(true)
    try {
      await onChangePassword(data)
      toast.success('Password changed successfully!')
      passwordForm.reset()
    } catch (error) {
      toast.error('Failed to change password')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendVerification = async (type: 'email' | 'phone') => {
    try {
      await onResendVerification(type)
      toast.success(`Verification ${type === 'email' ? 'email' : 'SMS'} sent!`)
    } catch (error) {
      toast.error(`Failed to send verification ${type === 'email' ? 'email' : 'SMS'}`)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="w-5 h-5 text-pink-500" />
        <h2 className="text-xl font-semibold text-gray-900">Security & Verification</h2>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'email', label: 'Email', icon: Mail },
            { id: 'phone', label: 'Phone', icon: Phone },
            { id: 'password', label: 'Password', icon: Lock },
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  "flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === tab.id
                    ? "border-pink-500 text-pink-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Email Tab */}
      {activeTab === 'email' && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Mail className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-900">Email Address</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Your email is used for account recovery and important notifications.
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={emailForm.handleSubmit(handleEmailSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="flex items-center gap-3">
                <input
                  {...emailForm.register('email')}
                  type="email"
                  className={cn(
                    "flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    emailForm.formState.errors.email ? "border-red-300" : "border-gray-300"
                  )}
                  placeholder="Enter your email address"
                />
                {initialData?.emailVerified && (
                  <CheckCircle className="w-5 h-5 text-green-500" title="Email verified" />
                )}
              </div>
              {emailForm.formState.errors.email && (
                <p className="mt-1 text-sm text-red-600">{emailForm.formState.errors.email.message}</p>
              )}
            </div>

            {!initialData?.emailVerified && initialData?.email && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm text-yellow-800">
                      Please verify your email address to secure your account.
                    </p>
                    <button
                      type="button"
                      onClick={() => handleResendVerification('email')}
                      className="mt-2 text-sm text-yellow-700 hover:text-yellow-800 underline"
                    >
                      Resend verification email
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                type="submit"
                disabled={isLoading || !emailForm.formState.isDirty}
                className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {isLoading ? 'Updating...' : 'Update Email'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Phone Tab */}
      {activeTab === 'phone' && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Phone className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-900">Phone Number</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Your phone number is used for account recovery and SMS notifications.
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={phoneForm.handleSubmit(handlePhoneSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <div className="flex items-center gap-3">
                <input
                  {...phoneForm.register('phone')}
                  type="tel"
                  className={cn(
                    "flex-1 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    phoneForm.formState.errors.phone ? "border-red-300" : "border-gray-300"
                  )}
                  placeholder="e.g., +************ or **********"
                />
                {initialData?.phoneVerified && (
                  <CheckCircle className="w-5 h-5 text-green-500" title="Phone verified" />
                )}
              </div>
              {phoneForm.formState.errors.phone && (
                <p className="mt-1 text-sm text-red-600">{phoneForm.formState.errors.phone.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-700">
                Enter your Kenyan phone number in international format (+254) or local format (07)
              </p>
            </div>

            {!initialData?.phoneVerified && initialData?.phone && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm text-yellow-800">
                      Please verify your phone number to secure your account.
                    </p>
                    <button
                      type="button"
                      onClick={() => handleResendVerification('phone')}
                      className="mt-2 text-sm text-yellow-700 hover:text-yellow-800 underline"
                    >
                      Resend verification SMS
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                type="submit"
                disabled={isLoading || !phoneForm.formState.isDirty}
                className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {isLoading ? 'Updating...' : 'Update Phone'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Password Tab */}
      {activeTab === 'password' && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Lock className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-900">Change Password</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Use a strong password with at least 8 characters, including uppercase, lowercase, and numbers.
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Password
              </label>
              <div className="relative">
                <input
                  {...passwordForm.register('currentPassword')}
                  type={showPassword ? 'text' : 'password'}
                  className={cn(
                    "w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    passwordForm.formState.errors.currentPassword ? "border-red-300" : "border-gray-300"
                  )}
                  placeholder="Enter your current password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {passwordForm.formState.errors.currentPassword && (
                <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.currentPassword.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                New Password
              </label>
              <div className="relative">
                <input
                  {...passwordForm.register('newPassword')}
                  type={showNewPassword ? 'text' : 'password'}
                  className={cn(
                    "w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    passwordForm.formState.errors.newPassword ? "border-red-300" : "border-gray-300"
                  )}
                  placeholder="Enter your new password"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showNewPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {passwordForm.formState.errors.newPassword && (
                <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.newPassword.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirm New Password
              </label>
              <input
                {...passwordForm.register('confirmPassword')}
                type="password"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                  passwordForm.formState.errors.confirmPassword ? "border-red-300" : "border-gray-300"
                )}
                placeholder="Confirm your new password"
              />
              {passwordForm.formState.errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.confirmPassword.message}</p>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {isLoading ? 'Changing...' : 'Change Password'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  )
} 
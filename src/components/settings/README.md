# Settings Components

A comprehensive, mobile-first settings page for the KenyaMatch dating web app with modular React components.

## 🎯 Features

### ✅ Mobile-First Responsive Design
- Optimized for mobile devices with responsive breakpoints
- Adaptive layouts that work on mobile, tablet, and desktop
- Touch-friendly interface elements

### ✅ Modular Component Architecture
- **ProfileSection**: Profile information and image upload
- **PreferencesSection**: Match preferences and criteria
- **SecuritySection**: Email/phone verification and password management
- **SubscriptionSection**: Subscription status and payment handling

### ✅ Form Validation & UX
- Zod schema validation for all forms
- React Hook Form integration
- Real-time validation feedback
- Loading states and error handling
- Toast notifications for user feedback

### ✅ Professional UI/UX
- Consistent design system with Tailwind CSS
- Accessible form controls
- Intuitive navigation with tabs
- Visual feedback for all interactions

## 📱 Components

### ProfileSection
Handles user profile information including:
- Profile picture upload (up to 6 images)
- Basic information (name, gender, date of birth, town)
- Cultural information (tribe, religion)
- Professional details (occupation, education)
- Relationship goals and bio

**Features:**
- Image upload with preview and removal
- Age validation (18+ requirement)
- Town selection from database
- Character counter for bio
- Form validation with error messages

### PreferencesSection
Manages match preferences and criteria:
- Gender preference selection
- Age range with min/max inputs
- Distance preference with slider
- Children preference options
- Cultural preferences (tribe, religion)

**Features:**
- Visual preference cards
- Real-time summary display
- Range slider for distance
- Age validation logic
- Responsive grid layouts

### SecuritySection
Handles account security and verification:
- Email address management
- Phone number verification
- Password change functionality
- Verification status indicators

**Features:**
- Tabbed interface for different security aspects
- Email/phone validation
- Password strength requirements
- Verification status badges
- Resend verification functionality

### SubscriptionSection
Manages subscription and billing:
- Subscription status display
- Payment method selection (Stripe/M-Pesa)
- Renewal and cancellation
- Billing information

**Features:**
- Visual subscription status cards
- Payment method selection
- M-Pesa integration
- Subscription expiry tracking
- Feature comparison display

## 🛠 Usage

### Basic Implementation

```tsx
import SettingsPage from '@/app/(protected)/settings/page'

export default function App() {
  return <SettingsPage />
}
```

### With Custom Data

```tsx
import ProfileSection from '@/components/settings/ProfileSection'

const profileData = {
  fullName: 'John Doe',
  gender: 'MALE',
  dateOfBirth: '1990-01-01',
  // ... other profile data
}

const towns = [
  { id: '1', name: 'Nairobi', county: 'Nairobi' },
  // ... more towns
]

function MySettingsPage() {
  const handleProfileSave = async (data) => {
    // Handle profile save logic
  }

  return (
    <ProfileSection
      initialData={profileData}
      towns={towns}
      onSave={handleProfileSave}
    />
  )
}
```

## 🎨 Design System

### Colors
- **Primary**: Pink-500 (#ec4899)
- **Success**: Green-500 (#10b981)
- **Warning**: Yellow-500 (#f59e0b)
- **Error**: Red-500 (#ef4444)
- **Neutral**: Gray scale (50-900)

### Typography
- **Headings**: Font-semibold, text-xl/text-2xl
- **Body**: Text-sm/text-base, text-gray-600/700
- **Labels**: Font-medium, text-gray-700

### Spacing
- **Section spacing**: space-y-6
- **Form spacing**: space-y-4
- **Component padding**: p-6 (mobile), p-8 (desktop)

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 📋 Form Validation Rules

### Profile
- **Name**: Minimum 2 characters
- **Age**: Must be 18+ years old
- **Town**: Required selection
- **Bio**: Maximum 500 characters

### Preferences
- **Age Range**: Min ≤ Max, both 18-100
- **Distance**: 1-500 km
- **Gender Preference**: Required selection

### Security
- **Email**: Valid email format
- **Phone**: Kenyan phone number format
- **Password**: 8+ chars, uppercase, lowercase, number

## 🔧 API Integration

### Required Endpoints

```typescript
// Profile
GET /api/users/profile/details
PUT /api/users/profile

// Preferences
GET /api/users/preferences
PUT /api/users/preferences

// Security
PUT /api/users/email
PUT /api/users/phone
PUT /api/users/password
POST /api/users/verify/{type}/resend

// Subscription
POST /api/payments
POST /api/subscriptions/cancel

// Towns
GET /api/towns
```

### Response Format

```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}
```

## 🧪 Testing

### Demo Data
Use the provided demo data for testing:

```tsx
import { 
  demoProfileData, 
  demoPreferencesData, 
  demoTowns,
  mockApi 
} from '@/lib/demo-data'
```

### Mock API
For development without backend:

```tsx
// Replace real API calls with mock
const data = await mockApi.fetchProfile()
```

## 📱 Mobile Considerations

### Touch Targets
- Minimum 44px touch targets
- Adequate spacing between interactive elements
- Full-width buttons on mobile

### Navigation
- Bottom tab navigation on mobile
- Sidebar navigation on desktop
- Smooth transitions between sections

### Form Inputs
- Large, easy-to-tap form controls
- Proper keyboard types for inputs
- Auto-focus management

## ♿ Accessibility

### ARIA Labels
- Proper labels for all form inputs
- Descriptive button text
- Screen reader friendly navigation

### Keyboard Navigation
- Tab order follows visual layout
- Enter/Space key support for buttons
- Escape key for modal dismissal

### Color Contrast
- WCAG AA compliant contrast ratios
- Color not used as sole indicator
- High contrast mode support

## 🚀 Performance

### Optimization
- Lazy loading of components
- Debounced form inputs
- Optimized image uploads
- Efficient re-renders

### Loading States
- Skeleton loaders for content
- Progressive loading
- Error boundaries
- Retry mechanisms

## 📝 Development Notes

### Dependencies
- React Hook Form for form management
- Zod for validation schemas
- Lucide React for icons
- React Hot Toast for notifications
- Tailwind CSS for styling

### File Structure
```
src/components/settings/
├── ProfileSection.tsx
├── PreferencesSection.tsx
├── SecuritySection.tsx
├── SubscriptionSection.tsx
└── README.md

src/components/ui/
└── Skeleton.tsx

src/lib/
├── utils.ts
└── demo-data.ts
```

### Environment Variables
Ensure these are configured:
- `NEXT_PUBLIC_APP_URL`
- `STRIPE_PUBLISHABLE_KEY`
- `MPESA_CONSUMER_KEY`
- `CLOUDINARY_CLOUD_NAME`

## 🔄 Updates & Maintenance

### Adding New Fields
1. Update Prisma schema
2. Add to component interfaces
3. Update validation schemas
4. Add to demo data
5. Update API endpoints

### Styling Changes
- Use Tailwind utility classes
- Follow design system guidelines
- Test on all breakpoints
- Ensure accessibility compliance

### Form Validation
- Add Zod schema rules
- Update error messages
- Test edge cases
- Add client-side validation

## 📞 Support

For questions or issues:
1. Check the demo data for examples
2. Review the API integration guide
3. Test with the mock API functions
4. Ensure all dependencies are installed

---

**Built with ❤️ for KenyaMatch** 
'use client'

import { useState } from 'react'
import { Crown, CreditCard, Smartphone, Calendar, CheckCircle, AlertTriangle, ArrowRight } from 'lucide-react'
import toast from 'react-hot-toast'
import { cn, formatDate } from '@/lib/utils'

interface SubscriptionSectionProps {
  subscription?: {
    isPaid: boolean
    subscriptionExpiresAt?: string
    planName?: string
    price?: number
    duration?: number
  }
  onRenewSubscription: (method: 'STRIPE' | 'MPESA') => Promise<void>
  onCancelSubscription: () => Promise<void>
}

export default function SubscriptionSection({ 
  subscription, 
  onRenewSubscription, 
  onCancelSubscription 
}: SubscriptionSectionProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'STRIPE' | 'MPESA'>('STRIPE')
  const [phoneNumber, setPhoneNumber] = useState('')

  const handleRenewal = async () => {
    setIsLoading(true)
    try {
      await onRenewSubscription(paymentMethod)
      if (paymentMethod === 'STRIPE') {
        toast.success('Redirecting to payment...')
      } else {
        toast.success('M-Pesa payment initiated!')
      }
    } catch (error) {
      toast.error('Failed to initiate payment')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancellation = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features.')) {
      return
    }
    
    setIsLoading(true)
    try {
      await onCancelSubscription()
      toast.success('Subscription cancelled successfully')
    } catch (error) {
      toast.error('Failed to cancel subscription')
    } finally {
      setIsLoading(false)
    }
  }

  const getDaysRemaining = () => {
    if (!subscription?.subscriptionExpiresAt) return 0
    const expiryDate = new Date(subscription.subscriptionExpiresAt)
    const today = new Date()
    const diffTime = expiryDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return Math.max(0, diffDays)
  }

  const daysRemaining = getDaysRemaining()
  const isExpired = daysRemaining === 0
  const isExpiringSoon = daysRemaining <= 7 && daysRemaining > 0

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Crown className="w-5 h-5 text-pink-500" />
        <h2 className="text-xl font-semibold text-gray-900">Subscription & Billing</h2>
      </div>

      {/* Subscription Status */}
      <div className="space-y-4">
        {subscription?.isPaid && !isExpired ? (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-green-900 mb-2">
                  Active Premium Subscription
                </h3>
                <div className="space-y-2 text-sm text-green-800">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>
                      Expires: {subscription.subscriptionExpiresAt ? formatDate(subscription.subscriptionExpiresAt) : 'Unknown'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Crown className="w-4 h-4" />
                    <span>Plan: {subscription.planName || 'Premium'}</span>
                  </div>
                  {daysRemaining > 0 && (
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      <span>
                        {daysRemaining === 1 
                          ? 'Expires tomorrow' 
                          : `${daysRemaining} days remaining`}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <AlertTriangle className="w-8 h-8 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-yellow-900 mb-2">
                  {isExpired ? 'Subscription Expired' : 'Premium Required'}
                </h3>
                <p className="text-sm text-yellow-800 mb-4">
                  {isExpired 
                    ? 'Your premium subscription has expired. Renew to continue enjoying all features.'
                    : 'Upgrade to premium to access matches, chat features, and advanced filters.'
                  }
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Subscription Features */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Premium Features</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {[
              { feature: 'Unlimited Matches', description: 'See and match with unlimited profiles' },
              { feature: 'Advanced Filters', description: 'Filter by tribe, religion, education, and more' },
              { feature: 'Priority Support', description: 'Get help faster with priority customer support' },
              { feature: 'Profile Boost', description: 'Get more visibility in search results' },
              { feature: 'Read Receipts', description: 'See when your messages are read' },
              { feature: 'Undo Last Swipe', description: 'Go back and change your last decision' },
            ].map((item, index) => (
              <div key={index} className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <div className="font-medium text-gray-900">{item.feature}</div>
                  <div className="text-sm text-gray-600">{item.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pricing */}
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 border border-pink-200 rounded-lg p-6">
          <div className="text-center mb-6">
            <div className="text-3xl font-bold text-pink-600 mb-2">
              KSh {subscription?.price || 1200}
            </div>
            <div className="text-gray-700">
              {subscription?.duration || 120} days of premium access
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Payment Method
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <label
                  className={cn(
                    "flex items-center gap-3 p-4 border-2 rounded-lg cursor-pointer transition-colors",
                    paymentMethod === 'STRIPE'
                      ? "border-pink-500 bg-pink-50 text-pink-700"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                >
                  <input
                    type="radio"
                    value="STRIPE"
                    checked={paymentMethod === 'STRIPE'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'STRIPE' | 'MPESA')}
                    className="sr-only"
                  />
                  <CreditCard className="w-5 h-5" />
                  <div>
                    <div className="font-medium">Credit/Debit Card</div>
                    <div className="text-sm text-gray-700 opacity-75">Visa, Mastercard, etc.</div>
                  </div>
                </label>

                <label
                  className={cn(
                    "flex items-center gap-3 p-4 border-2 rounded-lg cursor-pointer transition-colors",
                    paymentMethod === 'MPESA'
                      ? "border-pink-500 bg-pink-50 text-pink-700"
                      : "border-gray-200 hover:border-gray-300"
                  )}
                >
                  <input
                    type="radio"
                    value="MPESA"
                    checked={paymentMethod === 'MPESA'}
                    onChange={(e) => setPaymentMethod(e.target.value as 'STRIPE' | 'MPESA')}
                    className="sr-only"
                  />
                  <Smartphone className="w-5 h-5" />
                  <div>
                    <div className="font-medium">M-Pesa</div>
                    <div className="text-sm text-gray-700 opacity-75">Mobile money payment</div>
                  </div>
                </label>
              </div>
            </div>

            {paymentMethod === 'MPESA' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  M-Pesa Phone Number
                </label>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400"
                  placeholder="e.g., +************ or **********"
                />
                <p className="mt-1 text-xs text-gray-700">
                  Enter the phone number registered with your M-Pesa account
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                onClick={handleRenewal}
                disabled={isLoading || (paymentMethod === 'MPESA' && !phoneNumber)}
                className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  'Processing...'
                ) : (
                  <>
                    {subscription?.isPaid ? 'Renew Subscription' : 'Upgrade to Premium'}
                    <ArrowRight className="w-4 h-4" />
                  </>
                )}
              </button>

              {subscription?.isPaid && !isExpired && (
                <button
                  onClick={handleCancellation}
                  disabled={isLoading}
                  className="flex-1 sm:flex-none border border-red-300 text-red-700 font-medium py-3 px-6 rounded-lg hover:bg-red-50 transition-colors disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed"
                >
                  Cancel Subscription
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Billing Information */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Billing Information</h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Subscription Plan:</span>
              <span className="font-medium">{subscription?.planName || 'Premium'}</span>
            </div>
            <div className="flex justify-between">
              <span>Duration:</span>
              <span className="font-medium">{subscription?.duration || 120} days</span>
            </div>
            <div className="flex justify-between">
              <span>Price:</span>
              <span className="font-medium">KSh {subscription?.price || 1200}</span>
            </div>
            {subscription?.subscriptionExpiresAt && (
              <div className="flex justify-between">
                <span>Next Billing:</span>
                <span className="font-medium">{formatDate(subscription.subscriptionExpiresAt)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 
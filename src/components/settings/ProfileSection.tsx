'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Camera, Upload, X, User, MapPin, Users, GraduationCap, Heart } from 'lucide-react'
import toast from 'react-hot-toast'
import { cn } from '@/lib/utils'

const profileSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters'),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),
  dateOfBirth: z.string().refine((date) => {
    const age = new Date().getFullYear() - new Date(date).getFullYear()
    return age >= 18 && age <= 100
  }, 'You must be at least 18 years old'),
  town: z.string().min(1, 'Please select a town'),
  tribe: z.string().optional(),
  religion: z.string().optional(),
  occupation: z.string().optional(),
  education: z.string().optional(),
  relationshipGoal: z.enum(['marriage', 'serious', 'casual', 'friendship']).optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileSectionProps {
  initialData?: {
    fullName: string
    gender: 'MALE' | 'FEMALE' | 'OTHER'
    dateOfBirth: string
    town: string
    tribe?: string
    religion?: string
    occupation?: string
    education?: string
    relationshipGoal?: 'marriage' | 'serious' | 'casual' | 'friendship'
    bio?: string
    profilePictures?: Array<{ url: string; isPrimary: boolean }>
  }
  towns: Array<{ id: string; name: string; county: string }>
  onSave: (data: ProfileFormData) => Promise<void>
}

export default function ProfileSection({ initialData, towns, onSave }: ProfileSectionProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [previewImages, setPreviewImages] = useState<string[]>(
    initialData?.profilePictures?.map(pic => pic.url) || []
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: initialData?.fullName || '',
      gender: initialData?.gender || 'MALE',
      dateOfBirth: initialData?.dateOfBirth ? new Date(initialData.dateOfBirth).toISOString().split('T')[0] : '',
      town: initialData?.town || '',
      tribe: initialData?.tribe || '',
      religion: initialData?.religion || '',
      occupation: initialData?.occupation || '',
      education: initialData?.education || '',
      relationshipGoal: initialData?.relationshipGoal || 'serious',
      bio: initialData?.bio || '',
    },
  })

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    const validFiles = files.filter(file => 
      file.type.startsWith('image/') && file.size <= 5 * 1024 * 1024 // 5MB limit
    )

    if (validFiles.length + previewImages.length > 6) {
      toast.error('Maximum 6 images allowed')
      return
    }

    setUploadedImages(prev => [...prev, ...validFiles])
    
    // Create preview URLs
    validFiles.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreviewImages(prev => [...prev, e.target?.result as string])
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (index: number) => {
    setPreviewImages(prev => prev.filter((_, i) => i !== index))
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const onSubmit = async (data: ProfileFormData) => {
    setIsLoading(true)
    try {
      await onSave(data)
      toast.success('Profile updated successfully!')
    } catch (error) {
      toast.error('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <User className="w-5 h-5 text-pink-500" />
        <h2 className="text-xl font-semibold text-gray-900">Profile Information</h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Profile Pictures */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Profile Pictures
          </label>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {previewImages.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image}
                  alt={`Profile ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border-2 border-gray-200"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            {previewImages.length < 6 && (
              <label className="w-full h-24 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-pink-400 transition-colors">
                <Upload className="w-6 h-6 text-gray-400 mb-1" />
                <span className="text-xs text-gray-500">Add Photo</span>
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </label>
            )}
          </div>
          <p className="text-xs text-gray-700">
            Upload up to 6 photos. First photo will be your main profile picture.
          </p>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              {...register('fullName')}
              type="text"
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700",
                errors.fullName ? "border-red-300" : "border-gray-300"
              )}
              placeholder="Enter your full name"
            />
            {errors.fullName && (
              <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender *
            </label>
            <select
              {...register('gender')}
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700",
                errors.gender ? "border-red-300" : "border-gray-300"
              )}
            >
              <option value="MALE">Male</option>
              <option value="FEMALE">Female</option>
              <option value="OTHER">Other</option>
            </select>
            {errors.gender && (
              <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date of Birth *
            </label>
            <input
              {...register('dateOfBirth')}
              type="date"
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700",
                errors.dateOfBirth ? "border-red-300" : "border-gray-300"
              )}
              placeholder="YYYY-MM-DD"
            />
            {errors.dateOfBirth && (
              <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Town *
            </label>
            <select
              {...register('town')}
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700",
                errors.town ? "border-red-300" : "border-gray-300"
              )}
            >
              <option value="" disabled className="text-gray-600 opacity-100">Select a town</option>
              {towns.map((town) => (
                <option key={town.id} value={town.id} className="text-gray-900">
                  {town.name}, {town.county}
                </option>
              ))}
            </select>
            {errors.town && (
              <p className="mt-1 text-sm text-red-600">{errors.town.message}</p>
            )}
          </div>
        </div>

        {/* Cultural Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Users className="inline w-4 h-4 mr-1" />
              Tribe
            </label>
            <input
              {...register('tribe')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700"
              placeholder="e.g., Kikuyu, Luo, Kamba"
            />
            <p className="mt-1 text-xs text-gray-700">
              Leave empty to match with any tribe
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Religion
            </label>
            <input
              {...register('religion')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700"
              placeholder="e.g., Christian, Muslim, Traditional"
            />
            <p className="mt-1 text-xs text-gray-700">
              Leave empty to match with any religion
            </p>
          </div>
        </div>

        {/* Professional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <GraduationCap className="inline w-4 h-4 mr-1" />
              Occupation
            </label>
            <input
              {...register('occupation')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700"
              placeholder="e.g., Software Engineer, Teacher"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Education
            </label>
            <input
              {...register('education')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700"
              placeholder="e.g., University Degree, Diploma"
            />
          </div>
        </div>

        {/* Relationship Goal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Heart className="inline w-4 h-4 mr-1" />
            What are you looking for?
          </label>
          <select
            {...register('relationshipGoal')}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700"
          >
            <option value="serious">Serious Relationship</option>
            <option value="marriage">Marriage</option>
            <option value="casual">Casual Dating</option>
            <option value="friendship">Friendship</option>
          </select>
        </div>

        {/* Bio */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            About Me
          </label>
          <textarea
            {...register('bio')}
            rows={4}
            className={cn(
              "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors resize-none text-gray-900 bg-white placeholder-gray-400 disabled:text-gray-700 read-only:text-gray-700",
              errors.bio ? "border-red-300" : "border-gray-300"
            )}
            placeholder="Tell others about yourself, your interests, and what you're looking for..."
            maxLength={500}
          />
          <div className="flex justify-between items-center mt-1">
            {errors.bio && (
              <p className="text-sm text-red-600">{errors.bio.message}</p>
            )}
            <span className="text-xs text-gray-700 ml-auto">
              {watch('bio')?.length || 0}/500
            </span>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-200 disabled:text-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            {isLoading ? 'Saving...' : 'Save Profile'}
          </button>
          <button
            type="button"
            className="flex-1 sm:flex-none border border-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  )
} 
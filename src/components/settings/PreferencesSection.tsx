'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Heart, Users, MapPin, Baby, Filter } from 'lucide-react'
import toast from 'react-hot-toast'
import { cn } from '@/lib/utils'

const preferencesSchema = z.object({
  genderPreference: z.enum(['MALE', 'FEMALE', 'OTHER']),
  preferredAgeMin: z.number().min(18, 'Minimum age must be 18').max(100, 'Maximum age must be 100'),
  preferredAgeMax: z.number().min(18, 'Minimum age must be 18').max(100, 'Maximum age must be 100'),
  hasChildren: z.enum(['YES', 'NO', 'DOESNT_MATTER']),
  maxDistance: z.number().min(1, 'Distance must be at least 1km').max(500, 'Distance cannot exceed 500km'),
  tribePreference: z.string().optional(),
  religionPreference: z.string().optional(),
}).refine((data) => data.preferredAgeMin <= data.preferredAgeMax, {
  message: "Minimum age cannot be greater than maximum age",
  path: ["preferredAgeMax"],
})

type PreferencesFormData = z.infer<typeof preferencesSchema>

interface PreferencesSectionProps {
  initialData?: {
    genderPreference: 'MALE' | 'FEMALE' | 'OTHER'
    preferredAgeMin: number
    preferredAgeMax: number
    hasChildren: 'YES' | 'NO' | 'DOESNT_MATTER'
    maxDistance: number
    tribePreference?: string
    religionPreference?: string
  }
  onSave: (data: PreferencesFormData) => Promise<void>
}

export default function PreferencesSection({ initialData, onSave }: PreferencesSectionProps) {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
  } = useForm<PreferencesFormData>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      genderPreference: initialData?.genderPreference || 'FEMALE',
      preferredAgeMin: initialData?.preferredAgeMin || 25,
      preferredAgeMax: initialData?.preferredAgeMax || 35,
      hasChildren: initialData?.hasChildren || 'DOESNT_MATTER',
      maxDistance: initialData?.maxDistance || 50,
      tribePreference: initialData?.tribePreference || '',
      religionPreference: initialData?.religionPreference || '',
    },
  })

  const watchedValues = watch()

  const onSubmit = async (data: PreferencesFormData) => {
    setIsLoading(true)
    try {
      await onSave(data)
      toast.success('Preferences updated successfully!')
    } catch (error) {
      toast.error('Failed to update preferences')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Filter className="w-5 h-5 text-pink-500" />
        <h2 className="text-xl font-semibold text-gray-900">Match Preferences</h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Gender Preference */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Heart className="inline w-4 h-4 mr-1" />
            I'm interested in
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {[
              { value: 'FEMALE', label: 'Women' },
              { value: 'MALE', label: 'Men' },
              { value: 'OTHER', label: 'Everyone' },
            ].map((option) => (
              <label
                key={option.value}
                className={cn(
                  "flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-colors",
                  watchedValues.genderPreference === option.value
                    ? "border-pink-500 bg-pink-50 text-pink-700"
                    : "border-gray-200 hover:border-gray-300 text-gray-900"
                )}
              >
                <input
                  {...register('genderPreference')}
                  type="radio"
                  value={option.value}
                  className="sr-only"
                />
                <span className="font-medium">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Age Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Age Range
          </label>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-xs text-gray-500 mb-1">Minimum Age</label>
                <input
                  {...register('preferredAgeMin', { valueAsNumber: true })}
                  type="number"
                  min="18"
                  max="100"
                  className={cn(
                    "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    errors.preferredAgeMin ? "border-red-300" : "border-gray-300"
                  )}
                />
                {errors.preferredAgeMin && (
                  <p className="mt-1 text-sm text-red-600">{errors.preferredAgeMin.message}</p>
                )}
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-500 mb-1">Maximum Age</label>
                <input
                  {...register('preferredAgeMax', { valueAsNumber: true })}
                  type="number"
                  min="18"
                  max="100"
                  className={cn(
                    "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                    errors.preferredAgeMax ? "border-red-300" : "border-gray-300"
                  )}
                />
                {errors.preferredAgeMax && (
                  <p className="mt-1 text-sm text-red-600">{errors.preferredAgeMax.message}</p>
                )}
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">
                Age range: {watchedValues.preferredAgeMin} - {watchedValues.preferredAgeMax} years
              </div>
            </div>
          </div>
        </div>

        {/* Distance */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="inline w-4 h-4 mr-1" />
            Maximum Distance
          </label>
          <div className="space-y-4">
            <input
              {...register('maxDistance', { valueAsNumber: true })}
              type="range"
              min="1"
              max="500"
              step="5"
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>1 km</span>
              <span className="font-medium text-pink-600">{watchedValues.maxDistance} km</span>
              <span>500 km</span>
            </div>
            {errors.maxDistance && (
              <p className="text-sm text-red-600">{errors.maxDistance.message}</p>
            )}
          </div>
        </div>

        {/* Children Preference */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Baby className="inline w-4 h-4 mr-1" />
            Children Preference
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {[
              { value: 'DOESNT_MATTER', label: "Doesn't Matter", description: 'Any status' },
              { value: 'YES', label: 'Has Children', description: 'With kids' },
              { value: 'NO', label: 'No Children', description: 'Without kids' },
            ].map((option) => (
              <label
                key={option.value}
                className={cn(
                  "flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-colors",
                  watchedValues.hasChildren === option.value
                    ? "border-pink-500 bg-pink-50 text-pink-700"
                    : "border-gray-200 hover:border-gray-300 text-gray-900"
                )}
              >
                <input
                  {...register('hasChildren')}
                  type="radio"
                  value={option.value}
                  className="sr-only"
                />
                <span className="font-medium">{option.label}</span>
                <span className="text-xs opacity-75">{option.description}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Cultural Preferences */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Users className="inline w-4 h-4 mr-1" />
              Preferred Tribe (Optional)
            </label>
            <input
              {...register('tribePreference')}
              type="text"
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                errors.tribePreference ? "border-red-300" : "border-gray-300"
              )}
              placeholder="e.g., Kikuyu, Luo, Kamba"
            />
            <p className="mt-1 text-xs text-gray-500">
              Leave empty to match with any tribe
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Preferred Religion (Optional)
            </label>
            <input
              {...register('religionPreference')}
              type="text"
              className={cn(
                "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-colors text-gray-900 bg-white placeholder-gray-400",
                errors.religionPreference ? "border-red-300" : "border-gray-300"
              )}
              placeholder="e.g., Christian, Muslim, Traditional"
            />
            <p className="mt-1 text-xs text-gray-500">
              Leave empty to match with any religion
            </p>
          </div>
        </div>

        {/* Summary Card */}
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-6 rounded-lg border border-pink-100">
          <h3 className="font-semibold text-gray-900 mb-3">Your Match Criteria</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-700">Looking for:</span>
              <span className="ml-2 font-semibold text-gray-900">
                {watchedValues.genderPreference === 'FEMALE' ? 'Women' : 
                 watchedValues.genderPreference === 'MALE' ? 'Men' : 'Everyone'}
              </span>
            </div>
            <div>
              <span className="text-gray-700">Age range:</span>
              <span className="ml-2 font-semibold text-gray-900">
                {watchedValues.preferredAgeMin} - {watchedValues.preferredAgeMax} years
              </span>
            </div>
            <div>
              <span className="text-gray-700">Distance:</span>
              <span className="ml-2 font-semibold text-gray-900">Within {watchedValues.maxDistance} km</span>
            </div>
            <div>
              <span className="text-gray-700">Children:</span>
              <span className="ml-2 font-semibold text-gray-900">
                {watchedValues.hasChildren === 'DOESNT_MATTER' ? "Doesn't matter" :
                 watchedValues.hasChildren === 'YES' ? 'Has children' : 'No children'}
              </span>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="flex-1 bg-pink-500 hover:bg-pink-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            {isLoading ? 'Saving...' : 'Save Preferences'}
          </button>
          <button
            type="button"
            className="flex-1 sm:flex-none border border-gray-300 text-gray-700 font-medium py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Reset to Default
          </button>
        </div>
      </form>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #ec4899;
          cursor: pointer;
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #ec4899;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  )
} 
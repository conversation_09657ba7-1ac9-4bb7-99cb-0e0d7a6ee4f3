// Demo data for testing the settings components

export const demoTowns = [
  { id: '1', name: 'Nairobi', county: 'Nairobi' },
  { id: '2', name: 'Mombasa', county: 'Mombasa' },
  { id: '3', name: 'Kisumu', county: 'Kisumu' },
  { id: '4', name: 'Nakuru', county: 'Nakuru' },
  { id: '5', name: 'Eldoret', county: 'Uasin Gishu' },
  { id: '6', name: '<PERSON>hika', county: 'Kiambu' },
  { id: '7', name: 'Kakamega', county: 'Kakamega' },
  { id: '8', name: 'Nyeri', county: 'Nyeri' },
  { id: '9', name: 'Machakos', county: 'Machakos' },
  { id: '10', name: 'Kericho', county: 'Kericho' },
]

export const demoProfileData = {
  fullName: '<PERSON>',
  gender: 'FEMALE' as const,
  dateOfBirth: '1995-06-15',
  town: '1', // Nairobi
  tribe: '<PERSON><PERSON><PERSON>',
  religion: 'Christian',
  occupation: 'Software Engineer',
  education: 'University Degree',
  relationshipGoal: 'serious' as const,
  bio: 'I am a passionate software engineer who loves hiking, reading, and trying new restaurants. Looking for someone who shares similar values and is ready for a serious relationship.',
  profilePictures: [
    { url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face', isPrimary: true },
    { url: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop&crop=face', isPrimary: false },
    { url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face', isPrimary: false },
  ]
}

export const demoPreferencesData = {
  genderPreference: 'MALE' as const,
  preferredAgeMin: 28,
  preferredAgeMax: 38,
  hasChildren: 'DOESNT_MATTER' as const,
  maxDistance: 50,
  tribePreference: '',
  religionPreference: 'Christian'
}

export const demoUserData = {
  id: 'user-123',
  email: '<EMAIL>',
  phone: '+254700123456',
  isPaid: true,
  subscriptionExpiresAt: '2024-12-31T23:59:59Z',
  emailVerified: true,
  phoneVerified: false
}

export const demoSubscriptionData = {
  isPaid: true,
  subscriptionExpiresAt: '2024-12-31T23:59:59Z',
  planName: 'Premium',
  price: 1200,
  duration: 120
}

// Sample API responses for testing
export const demoApiResponses = {
  towns: {
    success: true,
    data: demoTowns
  },
  profile: {
    success: true,
    data: demoProfileData
  },
  preferences: {
    success: true,
    data: demoPreferencesData
  },
  user: {
    success: true,
    data: demoUserData
  },
  subscription: {
    success: true,
    data: demoSubscriptionData
  }
}

// Mock API functions for development
export const mockApi = {
  async fetchTowns() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    return demoApiResponses.towns
  },
  
  async fetchProfile() {
    await new Promise(resolve => setTimeout(resolve, 800))
    return demoApiResponses.profile
  },
  
  async fetchPreferences() {
    await new Promise(resolve => setTimeout(resolve, 600))
    return demoApiResponses.preferences
  },
  
  async fetchUser() {
    await new Promise(resolve => setTimeout(resolve, 400))
    return demoApiResponses.user
  },
  
  async updateProfile(data: any) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return { success: true, data }
  },
  
  async updatePreferences(data: any) {
    await new Promise(resolve => setTimeout(resolve, 800))
    return { success: true, data }
  },
  
  async updateEmail(data: any) {
    await new Promise(resolve => setTimeout(resolve, 600))
    return { success: true, data }
  },
  
  async updatePhone(data: any) {
    await new Promise(resolve => setTimeout(resolve, 600))
    return { success: true, data }
  },
  
  async changePassword(data: any) {
    await new Promise(resolve => setTimeout(resolve, 800))
    return { success: true, data }
  },
  
  async resendVerification(type: 'email' | 'phone') {
    await new Promise(resolve => setTimeout(resolve, 500))
    return { success: true, message: `${type} verification sent` }
  },
  
  async renewSubscription(method: 'STRIPE' | 'MPESA') {
    await new Promise(resolve => setTimeout(resolve, 1000))
    if (method === 'STRIPE') {
      return { 
        success: true, 
        data: { checkoutUrl: 'https://checkout.stripe.com/pay/test' }
      }
    }
    return { success: true, message: 'M-Pesa payment initiated' }
  },
  
  async cancelSubscription() {
    await new Promise(resolve => setTimeout(resolve, 800))
    return { success: true, message: 'Subscription cancelled' }
  }
} 
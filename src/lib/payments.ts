import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function createStripePaymentIntent(amount: number, currency: string = 'kes') {
  return stripe.paymentIntents.create({
    amount: amount * 100, // Convert to cents
    currency,
    automatic_payment_methods: {
      enabled: true,
    },
  })
}

export async function createMpesaPayment(phoneNumber: string, amount: number) {
  // This is a mock implementation for M-Pesa
  // In production, you would integrate with the actual M-Pesa API
  const response = await fetch('https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${await getMpesaAccessToken()}`,
    },
    body: JSON.stringify({
      BusinessShortCode: process.env.MPESA_BUSINESS_SHORTCODE,
      Password: generateMpesaPassword(),
      Timestamp: new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3),
      TransactionType: 'CustomerPayBillOnline',
      Amount: amount,
      PartyA: phoneNumber,
      PartyB: process.env.MPESA_BUSINESS_SHORTCODE,
      PhoneNumber: phoneNumber,
      CallBackURL: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/mpesa-callback`,
      AccountReference: 'KenyaMatch',
      TransactionDesc: 'KenyaMatch Subscription',
    }),
  })

  return response.json()
}

async function getMpesaAccessToken(): Promise<string> {
  const response = await fetch('https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials', {
    method: 'GET',
    headers: {
      'Authorization': `Basic ${Buffer.from(
        `${process.env.MPESA_CONSUMER_KEY}:${process.env.MPESA_CONSUMER_SECRET}`
      ).toString('base64')}`,
    },
  })

  const data = await response.json()
  return data.access_token
}

function generateMpesaPassword(): string {
  const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3)
  const password = `${process.env.MPESA_BUSINESS_SHORTCODE}${process.env.MPESA_PASSKEY}${timestamp}`
  return Buffer.from(password).toString('base64')
}

export function calculateSubscriptionExpiry(): Date {
  const days = parseInt(process.env.SUBSCRIPTION_DURATION_DAYS || '120')
  const expiryDate = new Date()
  expiryDate.setDate(expiryDate.getDate() + days)
  return expiryDate
} 
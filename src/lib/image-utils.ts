/**
 * Image utilities for robust image handling in KenyaMatch
 * Provides fallback mechanisms and URL validation
 */

/**
 * Validates if a URL is a valid image URL
 */
export function isValidImageUrl(url: string): boolean {
  if (!url) return false
  
  try {
    const parsedUrl = new URL(url)
    
    // Check if it's a supported protocol
    if (!['http:', 'https:', 'data:'].includes(parsedUrl.protocol)) {
      return false
    }
    
    // Check for common image extensions
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif', '.svg']
    const pathname = parsedUrl.pathname.toLowerCase()
    
    // If it has an image extension, it's likely valid
    if (imageExtensions.some(ext => pathname.endsWith(ext))) {
      return true
    }
    
    // Check for known image services that don't use extensions
    const imageServices = [
      'images.unsplash.com',
      'plus.unsplash.com',
      'via.placeholder.com',
      'picsum.photos',
      'placehold.co',
      'ui-avatars.com',
      'res.cloudinary.com',
      'storage.googleapis.com',
      'firebasestorage.googleapis.com'
    ]
    
    return imageServices.some(service => parsedUrl.hostname.includes(service))
  } catch {
    return false
  }
}

/**
 * Gets a fallback avatar URL based on user information
 */
export function getFallbackAvatarUrl(name: string, size: number = 200): string {
  const initials = name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2)
  
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&size=${size}&background=ec4899&color=ffffff&bold=true`
}

/**
 * Gets multiple fallback URLs for an image
 */
export function getImageFallbacks(originalUrl: string, alt: string, size: number = 200): string[] {
  const fallbacks = []
  
  // Original URL (if valid)
  if (isValidImageUrl(originalUrl)) {
    fallbacks.push(originalUrl)
  }
  
  // Generated avatar based on name
  fallbacks.push(getFallbackAvatarUrl(alt, size))
  
  // Placeholder service
  fallbacks.push(`https://via.placeholder.com/${size}x${size}/ec4899/ffffff?text=${encodeURIComponent(alt.charAt(0).toUpperCase())}`)
  
  // Local SVG placeholder
  fallbacks.push('/images/placeholder-avatar.svg')
  
  return fallbacks
}

/**
 * Optimizes image URL for better loading
 */
export function optimizeImageUrl(url: string, width?: number, height?: number): string {
  if (!url || !isValidImageUrl(url)) {
    return url
  }
  
  try {
    const parsedUrl = new URL(url)
    
    // Optimize Unsplash URLs
    if (parsedUrl.hostname.includes('unsplash.com')) {
      const params = new URLSearchParams(parsedUrl.search)
      
      if (width) params.set('w', width.toString())
      if (height) params.set('h', height.toString())
      params.set('fit', 'crop')
      params.set('crop', 'face')
      params.set('auto', 'format')
      params.set('q', '80')
      
      parsedUrl.search = params.toString()
      return parsedUrl.toString()
    }
    
    // Optimize Cloudinary URLs
    if (parsedUrl.hostname.includes('cloudinary.com')) {
      const pathParts = parsedUrl.pathname.split('/')
      const uploadIndex = pathParts.indexOf('upload')
      
      if (uploadIndex !== -1 && width && height) {
        const transformations = [`w_${width}`, `h_${height}`, 'c_fill', 'g_face', 'q_auto', 'f_auto']
        pathParts.splice(uploadIndex + 1, 0, transformations.join(','))
        parsedUrl.pathname = pathParts.join('/')
        return parsedUrl.toString()
      }
    }
    
    return url
  } catch {
    return url
  }
}

/**
 * Preloads an image to check if it's accessible
 */
export function preloadImage(src: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image()
    
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    
    // Set a timeout to avoid hanging
    setTimeout(() => resolve(false), 5000)
    
    img.src = src
  })
}

/**
 * Gets the best available image from a list of fallbacks
 */
export async function getBestAvailableImage(fallbacks: string[]): Promise<string> {
  for (const url of fallbacks) {
    if (await preloadImage(url)) {
      return url
    }
  }
  
  // Return the last fallback as a final option
  return fallbacks[fallbacks.length - 1] || '/images/placeholder-avatar.svg'
}

/**
 * Image configuration for Next.js Image component
 */
export const imageConfig = {
  domains: [
    'images.unsplash.com',
    'plus.unsplash.com',
    'via.placeholder.com',
    'picsum.photos',
    'placehold.co',
    'ui-avatars.com',
    'res.cloudinary.com',
    'storage.googleapis.com',
    'firebasestorage.googleapis.com',
    's3.amazonaws.com',
    'localhost',
    '127.0.0.1'
  ],
  formats: ['image/webp', 'image/avif'] as const,
  minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
}

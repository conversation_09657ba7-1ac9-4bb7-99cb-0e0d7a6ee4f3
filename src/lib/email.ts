// Email service for KenyaMatch
// This is a mock implementation - replace with actual email service (SendGrid, AWS SES, etc.)

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

// Email options interface for flexible parameter passing
interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
}

// Mock email sending function - internal version with individual parameters
async function sendEmailInternal(to: string, subject: string, html: string, text: string): Promise<void> {
  // In development, just log the email
  if (process.env.NODE_ENV === 'development') {
    console.log('📧 EMAIL SENT (DEV MODE):')
    console.log('To:', to)
    console.log('Subject:', subject)
    console.log('HTML:', html)
    console.log('Text:', text)
    console.log('---')
    return
  }

  // In production, integrate with actual email service
  // Example with SendGrid:
  /*
  const sgMail = require('@sendgrid/mail')
  sgMail.setApiKey(process.env.SENDGRID_API_KEY)

  const msg = {
    to,
    from: process.env.FROM_EMAIL,
    subject,
    text,
    html,
  }

  await sgMail.send(msg)
  */

  // For now, just simulate email sending
  await new Promise(resolve => setTimeout(resolve, 100))
}

// Exported email sending function with flexible parameters
export async function sendEmail(options: EmailOptions): Promise<void>
export async function sendEmail(to: string, subject: string, html: string, text?: string): Promise<void>
export async function sendEmail(
  optionsOrTo: EmailOptions | string,
  subject?: string,
  html?: string,
  text?: string
): Promise<void> {
  if (typeof optionsOrTo === 'object') {
    // Called with options object
    const { to, subject, html, text = '' } = optionsOrTo
    await sendEmailInternal(to, subject, html, text)
  } else {
    // Called with individual parameters
    await sendEmailInternal(optionsOrTo, subject!, html!, text || '')
  }
}

export async function sendVerificationEmail(email: string, name: string, verificationUrl: string): Promise<void> {
  const template = getVerificationEmailTemplate(name, verificationUrl)
  await sendEmailInternal(email, template.subject, template.html, template.text)
}

export async function sendPasswordResetEmail(email: string, name: string, resetUrl: string): Promise<void> {
  const template = getPasswordResetEmailTemplate(name, resetUrl)
  await sendEmailInternal(email, template.subject, template.html, template.text)
}

export async function sendWelcomeEmail(email: string, name: string): Promise<void> {
  const template = getWelcomeEmailTemplate(name)
  await sendEmailInternal(email, template.subject, template.html, template.text)
}

export async function sendMatchNotificationEmail(email: string, name: string, matchName: string): Promise<void> {
  const template = getMatchNotificationTemplate(name, matchName)
  await sendEmailInternal(email, template.subject, template.html, template.text)
}

export async function sendMessageNotificationEmail(email: string, name: string, senderName: string): Promise<void> {
  const template = getMessageNotificationTemplate(name, senderName)
  await sendEmailInternal(email, template.subject, template.html, template.text)
}

// Email Templates
function getVerificationEmailTemplate(name: string, verificationUrl: string): EmailTemplate {
  const subject = 'Verify your KenyaMatch email address'
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Verify Your Email - KenyaMatch</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💕 KenyaMatch</h1>
          <h2>Verify Your Email Address</h2>
        </div>
        <div class="content">
          <p>Hi ${name},</p>
          <p>Welcome to KenyaMatch! To complete your registration and start finding meaningful connections, please verify your email address.</p>
          <p>Click the button below to verify your email:</p>
          <a href="${verificationUrl}" class="button">Verify Email Address</a>
          <p>Or copy and paste this link in your browser:</p>
          <p><a href="${verificationUrl}">${verificationUrl}</a></p>
          <p>This verification link will expire in 24 hours.</p>
          <p>If you didn't create an account with KenyaMatch, please ignore this email.</p>
          <p>Best regards,<br>The KenyaMatch Team</p>
        </div>
        <div class="footer">
          <p>© 2024 KenyaMatch. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Hi ${name},
    
    Welcome to KenyaMatch! To complete your registration and start finding meaningful connections, please verify your email address.
    
    Click this link to verify your email: ${verificationUrl}
    
    This verification link will expire in 24 hours.
    
    If you didn't create an account with KenyaMatch, please ignore this email.
    
    Best regards,
    The KenyaMatch Team
  `
  
  return { subject, html, text }
}

function getPasswordResetEmailTemplate(name: string, resetUrl: string): EmailTemplate {
  const subject = 'Reset your KenyaMatch password'
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Reset Password - KenyaMatch</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💕 KenyaMatch</h1>
          <h2>Reset Your Password</h2>
        </div>
        <div class="content">
          <p>Hi ${name},</p>
          <p>We received a request to reset your KenyaMatch password.</p>
          <p>Click the button below to reset your password:</p>
          <a href="${resetUrl}" class="button">Reset Password</a>
          <p>Or copy and paste this link in your browser:</p>
          <p><a href="${resetUrl}">${resetUrl}</a></p>
          <p>This reset link will expire in 1 hour.</p>
          <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
          <p>Best regards,<br>The KenyaMatch Team</p>
        </div>
        <div class="footer">
          <p>© 2024 KenyaMatch. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Hi ${name},
    
    We received a request to reset your KenyaMatch password.
    
    Click this link to reset your password: ${resetUrl}
    
    This reset link will expire in 1 hour.
    
    If you didn't request a password reset, please ignore this email or contact support if you have concerns.
    
    Best regards,
    The KenyaMatch Team
  `
  
  return { subject, html, text }
}

function getWelcomeEmailTemplate(name: string): EmailTemplate {
  const subject = 'Welcome to KenyaMatch! 💕'
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Welcome to KenyaMatch</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💕 Welcome to KenyaMatch!</h1>
        </div>
        <div class="content">
          <p>Hi ${name},</p>
          <p>Welcome to Kenya's premier dating platform! We're excited to help you find meaningful connections and lasting relationships.</p>
          <p><strong>Here's how to get started:</strong></p>
          <ul>
            <li>Complete your profile with photos and details about yourself</li>
            <li>Set your preferences to find compatible matches</li>
            <li>Browse and connect with other singles in Kenya</li>
            <li>Start conversations and build meaningful relationships</li>
          </ul>
          <p>Remember to stay safe and report any suspicious activity. Our team is here to help you have the best experience possible.</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/matches" class="button">Start Matching</a>
          <p>Happy matching!</p>
          <p>Best regards,<br>The KenyaMatch Team</p>
        </div>
        <div class="footer">
          <p>© 2024 KenyaMatch. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Hi ${name},
    
    Welcome to Kenya's premier dating platform! We're excited to help you find meaningful connections and lasting relationships.
    
    Here's how to get started:
    - Complete your profile with photos and details about yourself
    - Set your preferences to find compatible matches
    - Browse and connect with other singles in Kenya
    - Start conversations and build meaningful relationships
    
    Remember to stay safe and report any suspicious activity. Our team is here to help you have the best experience possible.
    
    Visit ${process.env.NEXT_PUBLIC_APP_URL}/matches to start matching!
    
    Happy matching!
    
    Best regards,
    The KenyaMatch Team
  `
  
  return { subject, html, text }
}

function getMatchNotificationTemplate(name: string, matchName: string): EmailTemplate {
  const subject = `You have a new match with ${matchName}! 💕`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Match - KenyaMatch</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💕 New Match!</h1>
        </div>
        <div class="content">
          <p>Hi ${name},</p>
          <p>Great news! You have a new match with <strong>${matchName}</strong>!</p>
          <p>This could be the start of something special. Don't wait too long to start a conversation!</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/messages" class="button">Start Chatting</a>
          <p>Remember to be respectful and authentic in your conversations. Good luck!</p>
          <p>Best regards,<br>The KenyaMatch Team</p>
        </div>
        <div class="footer">
          <p>© 2024 KenyaMatch. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Hi ${name},
    
    Great news! You have a new match with ${matchName}!
    
    This could be the start of something special. Don't wait too long to start a conversation!
    
    Visit ${process.env.NEXT_PUBLIC_APP_URL}/messages to start chatting.
    
    Remember to be respectful and authentic in your conversations. Good luck!
    
    Best regards,
    The KenyaMatch Team
  `
  
  return { subject, html, text }
}

function getMessageNotificationTemplate(name: string, senderName: string): EmailTemplate {
  const subject = `New message from ${senderName} on KenyaMatch`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Message - KenyaMatch</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💬 New Message!</h1>
        </div>
        <div class="content">
          <p>Hi ${name},</p>
          <p>You have a new message from <strong>${senderName}</strong> on KenyaMatch!</p>
          <p>Don't keep them waiting - reply now to keep the conversation going!</p>
          <a href="${process.env.NEXT_PUBLIC_APP_URL}/messages" class="button">View Message</a>
          <p>Best regards,<br>The KenyaMatch Team</p>
        </div>
        <div class="footer">
          <p>© 2024 KenyaMatch. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Hi ${name},
    
    You have a new message from ${senderName} on KenyaMatch!
    
    Don't keep them waiting - reply now to keep the conversation going!
    
    Visit ${process.env.NEXT_PUBLIC_APP_URL}/messages to view the message.
    
    Best regards,
    The KenyaMatch Team
  `
  
  return { subject, html, text }
}

import { User, Profile, Match, Message, Payment, Town } from '@prisma/client'

export type Gender = 'MALE' | 'FEMALE' | 'OTHER'
export type PaymentMethod = 'MPESA' | 'STRIPE' | 'OTHER'
export type PaymentStatus = 'PENDING' | 'COMPLETED' | 'FAILED'
export type ChildrenPreference = 'YES' | 'NO' | 'DOESNT_MATTER'

export interface UserWithProfile extends User {
  profile: Profile & {
    town: Town
    profilePictures: Array<{
      id: string
      url: string
      isPrimary: boolean
    }>
  } | null
  children: {
    id: string
    count: number
    genders: Gender[]
  } | null
  preferences: {
    id: string
    preferredAgeMin: number
    preferredAgeMax: number
    hasChildren: ChildrenPreference
    maxDistance: number
    tribePreference: string | null
    religionPreference: string | null
  } | null
}

export interface MatchWithUsers extends Match {
  user1: UserWithProfile
  user2: UserWithProfile
}

export interface MessageWithUsers extends Message {
  sender: User
  receiver: User
}

export interface PaymentWithUser extends Payment {
  user: User
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface MatchRequest {
  targetUserId: string
}

export interface ProfileUpdateRequest {
  fullName?: string
  gender?: Gender
  genderPreference?: Gender
  dateOfBirth?: string
  townId?: string
  bio?: string
  tribe?: string
  religion?: string
  occupation?: string
}

export interface PreferencesUpdateRequest {
  preferredAgeMin?: number
  preferredAgeMax?: number
  hasChildren?: ChildrenPreference
  maxDistance?: number
  tribePreference?: string
  religionPreference?: string
}

export interface ChildrenUpdateRequest {
  count: number
  genders: Gender[]
}

export interface PaymentRequest {
  method: PaymentMethod
  amount: number
  phoneNumber?: string // For M-Pesa
}

export interface MessageRequest {
  matchId: string
  content: string
}

export interface ReportRequest {
  reportedUserId: string
  reason: string
}

export interface BlockUserRequest {
  blockedUserId: string
  reason?: string
}

export interface SearchFilters {
  gender?: Gender
  ageMin?: number
  ageMax?: number
  townId?: string
  hasChildren?: ChildrenPreference
  tribe?: string
  religion?: string
  maxDistance?: number
}

export interface NotificationData {
  type: 'match' | 'message' | 'payment' | 'system'
  title: string
  message: string
  metadata?: Record<string, any>
} 
import { NextRequest, NextResponse } from 'next/server'

/**
 * Image middleware for handling external image requests
 * Provides additional security and optimization for image loading
 */
export function imageMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Handle image optimization requests
  if (pathname.startsWith('/_next/image')) {
    const url = request.nextUrl.searchParams.get('url')
    
    if (url) {
      try {
        const imageUrl = new URL(decodeURIComponent(url))
        
        // Security check: only allow specific domains
        const allowedDomains = [
          'images.unsplash.com',
          'plus.unsplash.com',
          'via.placeholder.com',
          'picsum.photos',
          'placehold.co',
          'ui-avatars.com',
          'res.cloudinary.com',
          'storage.googleapis.com',
          'firebasestorage.googleapis.com',
          's3.amazonaws.com',
          'localhost',
          '127.0.0.1'
        ]
        
        const isAllowed = allowedDomains.some(domain => 
          imageUrl.hostname === domain || imageUrl.hostname.endsWith(`.${domain}`)
        )
        
        if (!isAllowed) {
          console.warn(`Blocked image request from unauthorized domain: ${imageUrl.hostname}`)
          return new NextResponse('Unauthorized image domain', { status: 403 })
        }
        
        // Add security headers for image responses
        const response = NextResponse.next()
        response.headers.set('X-Content-Type-Options', 'nosniff')
        response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
        
        return response
      } catch (error) {
        console.error('Error processing image URL:', error)
        return new NextResponse('Invalid image URL', { status: 400 })
      }
    }
  }

  return NextResponse.next()
}

/**
 * Configuration for image middleware
 */
export const imageMiddlewareConfig = {
  matcher: [
    '/_next/image/:path*',
  ],
}

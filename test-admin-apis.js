#!/usr/bin/env node

const BASE_URL = 'http://localhost:3000';

async function testAPI(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

async function getAdminToken() {
  console.log('🔐 Getting admin token...');
  const result = await testAPI('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'jack75522r'
    })
  });
  
  if (result.ok && result.data.success) {
    console.log('✅ Admin login successful');
    return result.data.data.token;
  } else {
    console.log('❌ Admin login failed:', result.data?.error || result.error);
    return null;
  }
}

async function testAdminEndpoints() {
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ Cannot proceed without admin token');
    return;
  }
  
  const authHeaders = {
    'Authorization': `Bearer ${token}`
  };
  
  console.log('\n📊 Testing admin endpoints...\n');
  
  // Test admin users
  console.log('1. Testing /api/admin/users');
  const usersResult = await testAPI('/api/admin/users', { headers: authHeaders });
  console.log(`   Status: ${usersResult.status}`);
  if (usersResult.ok) {
    console.log(`   ✅ Success: Found ${usersResult.data.users?.length || 0} users`);
  } else {
    console.log(`   ❌ Error: ${usersResult.data?.error || usersResult.error}`);
  }
  
  // Test admin stats
  console.log('\n2. Testing /api/admin/stats');
  const statsResult = await testAPI('/api/admin/stats', { headers: authHeaders });
  console.log(`   Status: ${statsResult.status}`);
  if (statsResult.ok) {
    console.log(`   ✅ Success: Stats retrieved`);
    console.log(`   Users: ${statsResult.data.totalUsers || 0}`);
    console.log(`   Matches: ${statsResult.data.totalMatches || 0}`);
  } else {
    console.log(`   ❌ Error: ${statsResult.data?.error || statsResult.error}`);
  }
  
  // Test admin payments
  console.log('\n3. Testing /api/admin/payments');
  const paymentsResult = await testAPI('/api/admin/payments', { headers: authHeaders });
  console.log(`   Status: ${paymentsResult.status}`);
  if (paymentsResult.ok) {
    console.log(`   ✅ Success: Found ${paymentsResult.data.payments?.length || 0} payments`);
  } else {
    console.log(`   ❌ Error: ${paymentsResult.data?.error || paymentsResult.error}`);
  }
  
  // Test admin reports
  console.log('\n4. Testing /api/admin/reports');
  const reportsResult = await testAPI('/api/admin/reports', { headers: authHeaders });
  console.log(`   Status: ${reportsResult.status}`);
  if (reportsResult.ok) {
    console.log(`   ✅ Success: Found ${reportsResult.data.reports?.length || 0} reports`);
  } else {
    console.log(`   ❌ Error: ${reportsResult.data?.error || reportsResult.error}`);
  }
  
  // Test payment stats
  console.log('\n5. Testing /api/admin/payments/stats');
  const paymentStatsResult = await testAPI('/api/admin/payments/stats', { headers: authHeaders });
  console.log(`   Status: ${paymentStatsResult.status}`);
  if (paymentStatsResult.ok) {
    console.log(`   ✅ Success: Payment stats retrieved`);
  } else {
    console.log(`   ❌ Error: ${paymentStatsResult.data?.error || paymentStatsResult.error}`);
  }
  
  console.log('\n🏁 Admin API testing completed!');
}

testAdminEndpoints().catch(console.error);

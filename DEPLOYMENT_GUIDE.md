# 🚀 KenyaMatch Deployment Guide

## 📋 Pre-Deployment Checklist

### ✅ **Environment Setup**
- [ ] Production database (PostgreSQL) configured
- [ ] Environment variables set in production
- [ ] SSL certificates configured
- [ ] Domain name configured
- [ ] CDN setup (optional but recommended)

### ✅ **Security**
- [ ] JWT secret keys are strong and unique
- [ ] Database credentials are secure
- [ ] API rate limiting configured
- [ ] CORS settings configured for production
- [ ] Input validation on all endpoints
- [ ] SQL injection protection (Prisma handles this)

### ✅ **Performance**
- [ ] Database indexes optimized
- [ ] Image optimization configured
- [ ] Caching strategy implemented
- [ ] Bundle size optimized

### ✅ **Monitoring**
- [ ] Error tracking (Sentry recommended)
- [ ] Performance monitoring
- [ ] Database monitoring
- [ ] Uptime monitoring

---

## 🌐 Vercel Deployment (Recommended)

### **1. Prepare for Deployment**

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### **2. Environment Variables**

Set these in Vercel dashboard or via CLI:

```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/database?sslmode=require"

# JWT
JWT_SECRET="your-super-secure-jwt-secret-key-here"

# App URL
NEXT_PUBLIC_APP_URL="https://your-domain.com"

# Email Service (if using SendGrid)
SENDGRID_API_KEY="your-sendgrid-api-key"
FROM_EMAIL="<EMAIL>"

# Payment Services
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_PUBLISHABLE_KEY="pk_live_..."
MPESA_CONSUMER_KEY="your-mpesa-consumer-key"
MPESA_CONSUMER_SECRET="your-mpesa-consumer-secret"
MPESA_SHORTCODE="your-mpesa-shortcode"
MPESA_PASSKEY="your-mpesa-passkey"

# File Upload (if using AWS S3)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket-name"
```

### **3. Database Setup**

```bash
# Run migrations in production
npx prisma migrate deploy

# Seed the database
npx prisma db seed
```

---

## 🗄️ Database Backup Strategy

### **Automated Daily Backups**

Create a backup script:

```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="kenyamatch"

# Create backup
pg_dump $DATABASE_URL > $BACKUP_DIR/kenyamatch_$DATE.sql

# Keep only last 30 days of backups
find $BACKUP_DIR -name "kenyamatch_*.sql" -mtime +30 -delete

echo "Backup completed: kenyamatch_$DATE.sql"
```

### **Setup Cron Job**

```bash
# Add to crontab (crontab -e)
0 2 * * * /path/to/backup-db.sh
```

---

## 📧 Email Service Setup

### **Option 1: SendGrid (Recommended)**

1. Sign up at [SendGrid](https://sendgrid.com)
2. Create API key
3. Verify sender identity
4. Update environment variables

### **Option 2: AWS SES**

1. Setup AWS SES
2. Verify domain
3. Request production access
4. Update email service in `src/lib/email.ts`

---

## 📱 PWA Configuration

### **Service Worker Registration**

The service worker is automatically registered in the layout. For production:

1. Test PWA functionality locally
2. Verify manifest.json is accessible
3. Test offline functionality
4. Verify push notifications (if implemented)

### **App Store Submission (Optional)**

For native app stores, consider using:
- **PWA Builder** by Microsoft
- **Capacitor** by Ionic
- **Cordova**

---

## 🔒 Security Hardening

### **Headers Configuration**

Add to `next.config.js`:

```javascript
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]
  }
}
```

### **Rate Limiting**

Implement rate limiting for API routes:

```javascript
// middleware/rateLimit.js
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '10 s'),
})

export async function rateLimit(request) {
  const ip = request.ip ?? '127.0.0.1'
  const { success } = await ratelimit.limit(ip)
  return success
}
```

---

## 📊 Monitoring Setup

### **Error Tracking with Sentry**

```bash
npm install @sentry/nextjs
```

```javascript
// sentry.client.config.js
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
})
```

### **Performance Monitoring**

1. **Vercel Analytics** (built-in)
2. **Google Analytics**
3. **Mixpanel** for user analytics

---

## 🧪 Testing in Production

### **Smoke Tests**

Run the test suite against production:

```bash
# Update BASE_URL in tests/api.test.js to production URL
node tests/api.test.js
```

### **Load Testing**

Use tools like:
- **Artillery**
- **k6**
- **Apache Bench**

---

## 📈 Performance Optimization

### **Image Optimization**

```javascript
// next.config.js
const nextConfig = {
  images: {
    domains: ['your-cdn-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

### **Bundle Analysis**

```bash
npm install @next/bundle-analyzer
```

---

## 🚨 Incident Response

### **Monitoring Alerts**

Set up alerts for:
- High error rates
- Slow response times
- Database connection issues
- High memory usage

### **Rollback Strategy**

1. Keep previous deployment ready
2. Database migration rollback plan
3. Feature flags for quick disabling

---

## 📞 Support & Maintenance

### **Regular Tasks**

- [ ] Weekly database performance review
- [ ] Monthly security updates
- [ ] Quarterly dependency updates
- [ ] User feedback review

### **Emergency Contacts**

- Database admin: [contact]
- DevOps team: [contact]
- Security team: [contact]

---

## 🎯 Go-Live Checklist

### **Final Steps**

- [ ] All tests passing
- [ ] Performance benchmarks met
- [ ] Security scan completed
- [ ] Backup strategy tested
- [ ] Monitoring configured
- [ ] DNS configured
- [ ] SSL certificate active
- [ ] CDN configured (if applicable)
- [ ] Error tracking active
- [ ] Team notified

### **Post-Launch**

- [ ] Monitor error rates for 24 hours
- [ ] Check performance metrics
- [ ] Verify all features working
- [ ] User acceptance testing
- [ ] Gather initial feedback

---

**🎉 Congratulations! KenyaMatch is ready for production!**

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function verifySeed() {
  console.log('🔍 Verifying database seed data...\n')

  // Count all records
  const counts = {
    users: await prisma.user.count(),
    profiles: await prisma.profile.count(),
    towns: await prisma.town.count(),
    subscriptionPlans: await prisma.subscriptionPlan.count(),
    matches: await prisma.match.count(),
    messages: await prisma.message.count(),
    payments: await prisma.payment.count(),
    reports: await prisma.report.count(),
    blockedUsers: await prisma.blockedUser.count(),
    notifications: await prisma.notification.count(),
    userActivities: await prisma.userActivity.count(),
    searchHistory: await prisma.searchHistory.count(),
    profilePictures: await prisma.profilePicture.count()
  }

  console.log('📊 Database Record Counts:')
  Object.entries(counts).forEach(([table, count]) => {
    console.log(`   ${table}: ${count}`)
  })

  // Show admin user
  const admin = await prisma.user.findFirst({
    where: { role: 'ADMIN' },
    include: { profile: true }
  })
  
  console.log('\n👨‍💼 Admin User:')
  console.log(`   Email: ${admin?.email}`)
  console.log(`   Name: ${admin?.profile?.fullName}`)

  // Show sample users
  const sampleUsers = await prisma.user.findMany({
    where: { role: 'USER' },
    include: { profile: true },
    take: 5
  })

  console.log('\n👥 Sample Users:')
  sampleUsers.forEach(user => {
    console.log(`   ${user.profile?.fullName} (${user.email}) - ${user.isPaid ? 'Paid' : 'Free'}`)
  })

  // Show towns
  const towns = await prisma.town.findMany()
  console.log('\n🏘️ Towns:')
  towns.forEach(town => {
    console.log(`   ${town.name}, ${town.county}`)
  })

  // Show subscription plans
  const plans = await prisma.subscriptionPlan.findMany()
  console.log('\n💳 Subscription Plans:')
  plans.forEach(plan => {
    console.log(`   ${plan.name}: KES ${plan.price} for ${plan.duration} days`)
  })

  // Show report statistics
  const reportStats = await prisma.report.groupBy({
    by: ['status'],
    _count: { status: true }
  })
  
  console.log('\n📋 Report Statistics:')
  reportStats.forEach(stat => {
    console.log(`   ${stat.status}: ${stat._count.status}`)
  })

  // Show recent reports
  const recentReports = await prisma.report.findMany({
    include: {
      reporter: { select: { email: true, profile: { select: { fullName: true } } } },
      reported: { select: { email: true, profile: { select: { fullName: true } } } }
    },
    take: 3,
    orderBy: { createdAt: 'desc' }
  })

  console.log('\n🚨 Recent Reports:')
  recentReports.forEach(report => {
    console.log(`   ${report.reason} - ${report.status}`)
    console.log(`     Reporter: ${report.reporter.profile?.fullName}`)
    console.log(`     Reported: ${report.reported.profile?.fullName}`)
  })

  console.log('\n✅ Database verification completed!')
}

verifySeed()
  .catch((e) => {
    console.error('❌ Error verifying seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test image configuration and validate external image URLs
 */

import { readFileSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Read next.config.js
const configPath = join(__dirname, '..', 'next.config.js')

console.log('🖼️  Testing Image Configuration...\n')

// Test URLs that should be allowed
const testUrls = [
  'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=400&fit=crop&crop=face',
  'https://plus.unsplash.com/premium_photo-1234567890',
  'https://via.placeholder.com/400x400/ec4899/ffffff',
  'https://picsum.photos/400/400',
  'https://ui-avatars.com/api/?name=<PERSON>+<PERSON><PERSON>&size=400&background=ec4899&color=ffffff',
  'https://res.cloudinary.com/demo/image/upload/sample.jpg',
  'https://storage.googleapis.com/bucket/image.jpg',
  'https://s3.amazonaws.com/bucket/image.jpg',
  'https://mybucket.s3.amazonaws.com/image.jpg',
  'https://mybucket.blob.core.windows.net/container/image.jpg',
  'https://firebasestorage.googleapis.com/v0/b/project/o/image.jpg',
]

// Function to validate URL against patterns
function validateUrl(url, patterns) {
  try {
    const urlObj = new URL(url)
    
    return patterns.some(pattern => {
      if (pattern.hostname.includes('*')) {
        // Handle wildcard patterns
        const regex = new RegExp(
          '^' + pattern.hostname.replace(/\*/g, '.*') + '$'
        )
        return regex.test(urlObj.hostname)
      }
      return urlObj.hostname === pattern.hostname
    })
  } catch (error) {
    return false
  }
}

// Mock remote patterns (since we can't easily import the config)
const mockRemotePatterns = [
  { protocol: 'https', hostname: 'images.unsplash.com', pathname: '/**' },
  { protocol: 'https', hostname: 'plus.unsplash.com', pathname: '/**' },
  { protocol: 'https', hostname: 'via.placeholder.com', pathname: '/**' },
  { protocol: 'https', hostname: 'picsum.photos', pathname: '/**' },
  { protocol: 'https', hostname: 'placehold.co', pathname: '/**' },
  { protocol: 'https', hostname: 'ui-avatars.com', pathname: '/**' },
  { protocol: 'https', hostname: 'res.cloudinary.com', pathname: '/**' },
  { protocol: 'https', hostname: 'storage.googleapis.com', pathname: '/**' },
  { protocol: 'https', hostname: 's3.amazonaws.com', pathname: '/**' },
  { protocol: 'https', hostname: '*.s3.amazonaws.com', pathname: '/**' },
  { protocol: 'https', hostname: 's3.*.amazonaws.com', pathname: '/**' },
  { protocol: 'https', hostname: '*.blob.core.windows.net', pathname: '/**' },
  { protocol: 'https', hostname: 'firebasestorage.googleapis.com', pathname: '/**' },
  { protocol: 'https', hostname: '*.supabase.co', pathname: '/storage/**' },
]

console.log('Testing URLs against remote patterns:\n')

testUrls.forEach((url, index) => {
  const isValid = validateUrl(url, mockRemotePatterns)
  const status = isValid ? '✅ ALLOWED' : '❌ BLOCKED'
  console.log(`${index + 1}. ${status}`)
  console.log(`   ${url}`)
  console.log()
})

console.log('📋 Summary:')
console.log(`- Total URLs tested: ${testUrls.length}`)
console.log(`- Allowed: ${testUrls.filter(url => validateUrl(url, mockRemotePatterns)).length}`)
console.log(`- Blocked: ${testUrls.filter(url => !validateUrl(url, mockRemotePatterns)).length}`)

console.log('\n🔧 Configuration Tips:')
console.log('1. Make sure package.json has "type": "module"')
console.log('2. Restart Next.js dev server after config changes')
console.log('3. Clear browser cache if images still fail to load')
console.log('4. Check browser console for specific error messages')
console.log('5. Use the SafeImage component for robust image handling')

console.log('\n✨ Done!')

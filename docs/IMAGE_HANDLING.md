# Image Handling in KenyaMatch

This document describes the robust and future-proof image handling system implemented in KenyaMatch.

## Overview

The image handling system provides:
- ✅ Multiple fallback mechanisms
- ✅ Automatic URL optimization
- ✅ Security validation
- ✅ Error boundaries and retry logic
- ✅ Loading states and smooth transitions
- ✅ Support for various image services
- ✅ Development and production optimizations

## Components

### 1. SafeImage Component (`src/components/ui/safe-image.tsx`)

The main image component that handles all edge cases:

```tsx
import { SafeImage, SafeAvatar, SafeProfilePicture } from '@/components/ui/safe-image'

// Basic usage
<SafeImage 
  src="https://images.unsplash.com/photo-123" 
  alt="User photo" 
  width={400} 
  height={400} 
/>

// Avatar usage
<SafeAvatar 
  src="https://images.unsplash.com/photo-123" 
  alt="John Doe" 
  size={40} 
/>

// Profile picture usage
<SafeProfilePicture 
  src="https://images.unsplash.com/photo-123" 
  alt="John Doe" 
  size={200} 
/>
```

### 2. Image Utilities (`src/lib/image-utils.ts`)

Utility functions for image processing:

```tsx
import { 
  isValidImageUrl, 
  getFallbackAvatarUrl, 
  getImageFallbacks, 
  optimizeImageUrl 
} from '@/lib/image-utils'

// Validate image URL
const isValid = isValidImageUrl('https://example.com/image.jpg')

// Get fallback avatar
const fallbackUrl = getFallbackAvatarUrl('John Doe', 200)

// Get multiple fallbacks
const fallbacks = getImageFallbacks(originalUrl, 'John Doe', 200)

// Optimize URL for better loading
const optimizedUrl = optimizeImageUrl(url, 400, 400)
```

### 3. Error Boundary (`src/components/ui/image-error-boundary.tsx`)

Catches and handles image-related errors:

```tsx
import { ImageErrorBoundary } from '@/components/ui/image-error-boundary'

<ImageErrorBoundary fallback={<div>Custom error UI</div>}>
  <YourImageComponent />
</ImageErrorBoundary>
```

## Configuration

### Next.js Configuration (`next.config.js`)

The configuration includes:

- **Remote Patterns**: Allowed external image domains
- **Security Headers**: Content security policies
- **Optimization**: WebP/AVIF formats, caching
- **Development Mode**: Unoptimized images for faster development

### Allowed Domains

The system supports images from:

- **Unsplash**: `images.unsplash.com`, `plus.unsplash.com`
- **Placeholder Services**: `via.placeholder.com`, `picsum.photos`, `placehold.co`
- **Avatar Services**: `ui-avatars.com`
- **Cloud Storage**: 
  - Cloudinary: `res.cloudinary.com`
  - Google Cloud: `storage.googleapis.com`
  - AWS S3: `s3.amazonaws.com`, `*.s3.amazonaws.com`
  - Azure Blob: `*.blob.core.windows.net`
  - Firebase: `firebasestorage.googleapis.com`
  - Supabase: `*.supabase.co`

## Fallback Strategy

When an image fails to load, the system tries:

1. **Original URL** - The provided image URL
2. **Generated Avatar** - UI Avatars service with user initials
3. **Placeholder Service** - Via.placeholder.com with user initial
4. **Local SVG** - Local placeholder SVG file

## Features

### Automatic Optimization

- **Unsplash URLs**: Adds width, height, crop, and quality parameters
- **Cloudinary URLs**: Adds transformation parameters
- **Caching**: 30-day cache TTL for optimized loading

### Security

- **Domain Validation**: Only allowed domains can load images
- **Content Security Policy**: Prevents XSS attacks via images
- **SVG Sanitization**: Safe handling of SVG images

### Performance

- **Lazy Loading**: Images load only when needed
- **WebP/AVIF**: Modern formats for better compression
- **Progressive Loading**: Smooth transitions with loading states
- **Retry Logic**: Automatic retries with exponential backoff

## Usage Examples

### Basic Image with Fallback

```tsx
<SafeImage
  src="https://images.unsplash.com/photo-123"
  alt="Beautiful landscape"
  width={800}
  height={600}
  className="rounded-lg"
  showLoader={true}
  maxRetries={3}
/>
```

### User Avatar with Multiple Fallbacks

```tsx
<SafeAvatar
  src={user.profilePicture}
  alt={user.name}
  size={64}
  className="border-2 border-white shadow-lg"
/>
```

### Profile Picture Gallery

```tsx
{user.profilePictures.map((pic, index) => (
  <SafeProfilePicture
    key={index}
    src={pic.url}
    alt={`${user.name} photo ${index + 1}`}
    size={300}
    priority={index === 0}
  />
))}
```

## Testing

Run the image configuration test:

```bash
npm run test:images
```

This will validate that your configuration allows the expected image domains.

## Troubleshooting

### Common Issues

1. **"hostname not configured" Error**
   - Add the domain to `remotePatterns` in `next.config.js`
   - Restart the development server
   - Clear browser cache

2. **Images Not Loading in Production**
   - Verify the domain is in the production config
   - Check network connectivity to image services
   - Review server logs for blocked requests

3. **Slow Image Loading**
   - Use `priority={true}` for above-the-fold images
   - Implement proper `sizes` attribute
   - Consider using `placeholder="blur"` with `blurDataURL`

### Development Tips

1. **Use SafeImage Components**: Always use the safe image components instead of Next.js Image directly
2. **Test Fallbacks**: Temporarily break image URLs to test fallback behavior
3. **Monitor Console**: Check browser console for image-related warnings
4. **Optimize URLs**: Use the image utilities to optimize external URLs

## Future Enhancements

- [ ] Image compression service integration
- [ ] Advanced caching strategies
- [ ] Image format detection and conversion
- [ ] Automatic alt text generation
- [ ] Performance monitoring and analytics
- [ ] CDN integration for better global performance

## Best Practices

1. **Always Provide Alt Text**: Essential for accessibility
2. **Use Appropriate Sizes**: Match image dimensions to display size
3. **Implement Loading States**: Provide visual feedback during loading
4. **Handle Errors Gracefully**: Always have fallback options
5. **Optimize for Performance**: Use modern formats and proper caching
6. **Test Edge Cases**: Verify behavior with broken URLs and slow networks

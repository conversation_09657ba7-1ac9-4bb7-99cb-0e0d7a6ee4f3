/*
  Warnings:

  - A unique constraint covering the columns `[profileId,type]` on the table `UserVerification` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Report" ADD COLUMN     "reviewedAt" TIMESTAMP(3),
ADD COLUMN     "reviewedBy" TEXT;

-- CreateIndex
CREATE INDEX "Report_reviewedBy_idx" ON "Report"("reviewedBy");

-- CreateIndex
CREATE UNIQUE INDEX "UserVerification_profileId_type_key" ON "UserVerification"("profileId", "type");

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

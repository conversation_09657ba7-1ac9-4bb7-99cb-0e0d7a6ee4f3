-- KenyaMatch Database Setup Script
-- Run this script in your PostgreSQL database

-- Create database (run this as superuser)
-- CREATE DATABASE kenyamatch;

-- Connect to the database and run the following:

-- Create user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_user WHERE usename = 'postgres') THEN
        -- Use environment variable for password in production
        CREATE USER postgres WITH PASSWORD '<DB_PASSWORD_HERE>';
    END IF;
END
$$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE kenyamatch TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Set timezone
SET timezone = 'Africa/Nairobi'; 
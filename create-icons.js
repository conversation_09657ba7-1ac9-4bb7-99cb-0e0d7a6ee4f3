// Simple script to create placeholder PWA icons
const fs = require('fs');
const path = require('path');

// Create icons directory
const iconsDir = path.join(__dirname, 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Icon sizes needed for PWA
const sizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512];

// Create simple SVG icon template
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#ec4899"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/3}" fill="white"/>
  <text x="${size/2}" y="${size/2 + 8}" text-anchor="middle" fill="#ec4899" font-family="Arial, sans-serif" font-size="${size/8}" font-weight="bold">KM</text>
</svg>
`;

// Generate icons
sizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.png`;
  const svgFilename = `icon-${size}x${size}.svg`;
  
  // Save SVG version (browsers can use these as fallback)
  fs.writeFileSync(path.join(iconsDir, svgFilename), svgContent);
  
  console.log(`Created ${svgFilename}`);
});

console.log('PWA icons created successfully!');
console.log('Note: For production, replace these with proper PNG icons using a tool like ImageMagick or online converters.');

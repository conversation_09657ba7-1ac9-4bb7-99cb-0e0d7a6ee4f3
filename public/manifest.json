{"name": "KenyaMatch - Find Love in Kenya", "short_name": "KenyaMatch", "description": "Kenya's premier dating platform for meaningful connections and lasting relationships", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#ec4899", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["social", "lifestyle"], "icons": [{"src": "/icons/icon-72x72.svg", "sizes": "72x72", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.svg", "sizes": "96x96", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.svg", "sizes": "128x128", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.svg", "sizes": "144x144", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.svg", "sizes": "152x152", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.svg", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.svg", "sizes": "384x384", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.svg", "sizes": "512x512", "type": "image/svg+xml", "purpose": "maskable any"}], "shortcuts": [{"name": "Find Matches", "short_name": "Matches", "description": "Browse potential matches", "url": "/matches", "icons": [{"src": "/icons/icon-96x96.svg", "sizes": "96x96"}]}, {"name": "Messages", "short_name": "Cha<PERSON>", "description": "View your conversations", "url": "/messages", "icons": [{"src": "/icons/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Profile", "short_name": "Profile", "description": "Edit your profile", "url": "/profile", "icons": [{"src": "/icons/icon-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "KenyaMatch homepage on desktop"}, {"src": "/screenshots/mobile-matches.png", "sizes": "375x812", "type": "image/png", "form_factor": "narrow", "label": "Browse matches on mobile"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}}
// Basic API tests for KenyaMatch
// Run with: node tests/api.test.js

const BASE_URL = 'http://localhost:3000';

// Test utilities
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✓ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}✗ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠ ${msg}${colors.reset}`)
};

// Test data
const testUser = {
  email: `test${Date.now()}@example.com`,
  password: 'testpassword123',
  phone: `+254${Math.floor(Math.random() * 1000000000)}`
};

let authToken = null;

// Test functions
async function testAPI(url, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` }),
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data, ok: response.ok };
  } catch (error) {
    return { error: error.message, status: 0 };
  }
}

async function testUserRegistration() {
  log.info('Testing user registration...');
  
  const result = await testAPI('/api/auth/register', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  if (result.ok && result.data.success) {
    log.success('User registration successful');
    return true;
  } else {
    log.error(`Registration failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testUserLogin() {
  log.info('Testing user login...');
  
  const result = await testAPI('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password
    })
  });
  
  if (result.ok && result.data.success && result.data.token) {
    authToken = result.data.token;
    log.success('User login successful');
    return true;
  } else {
    log.error(`Login failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testGetTowns() {
  log.info('Testing towns API...');
  
  const result = await testAPI('/api/towns');
  
  if (result.ok && result.data.success && Array.isArray(result.data.data)) {
    log.success(`Towns API working - ${result.data.data.length} towns loaded`);
    return true;
  } else {
    log.error(`Towns API failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testProtectedRoute() {
  log.info('Testing protected route (matches)...');
  
  const result = await testAPI('/api/matches');
  
  if (result.status === 403 && result.data.error?.includes('subscription')) {
    log.success('Protected route working - subscription required');
    return true;
  } else if (result.ok && result.data.success) {
    log.success('Protected route working - matches returned');
    return true;
  } else {
    log.error(`Protected route failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testAdvancedSearch() {
  log.info('Testing advanced search API...');
  
  const result = await testAPI('/api/matches/search', {
    method: 'POST',
    body: JSON.stringify({
      gender: 'FEMALE',
      minAge: 25,
      maxAge: 35,
      page: 1,
      limit: 10
    })
  });
  
  if (result.status === 403 && result.data.error?.includes('subscription')) {
    log.success('Advanced search working - subscription required');
    return true;
  } else if (result.ok && result.data.success) {
    log.success('Advanced search working - results returned');
    return true;
  } else {
    log.error(`Advanced search failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testFilterOptions() {
  log.info('Testing filter options API...');
  
  const result = await testAPI('/api/matches/filters');
  
  if (result.ok && result.data.success && result.data.data) {
    const { towns, tribes, religions, genders } = result.data.data;
    if (towns && tribes && religions && genders) {
      log.success('Filter options API working - all filter data available');
      return true;
    }
  }
  
  log.error(`Filter options failed: ${result.data?.error || result.error}`);
  return false;
}

async function testEmailVerification() {
  log.info('Testing email verification API...');
  
  const result = await testAPI('/api/auth/verify-email', {
    method: 'POST'
  });
  
  if (result.ok && result.data.success) {
    log.success('Email verification API working');
    return true;
  } else if (result.status === 400 || result.status === 404) {
    log.success('Email verification API working - proper error handling');
    return true;
  } else {
    log.error(`Email verification failed: ${result.data?.error || result.error}`);
    return false;
  }
}

async function testPasswordReset() {
  log.info('Testing password reset API...');
  
  const result = await testAPI('/api/auth/forgot-password', {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>'
    })
  });
  
  if (result.ok && result.data.success) {
    log.success('Password reset API working');
    return true;
  } else {
    log.error(`Password reset failed: ${result.data?.error || result.error}`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log(`${colors.blue}🧪 KenyaMatch API Tests${colors.reset}\n`);
  
  const tests = [
    { name: 'User Registration', fn: testUserRegistration },
    { name: 'User Login', fn: testUserLogin },
    { name: 'Towns API', fn: testGetTowns },
    { name: 'Protected Routes', fn: testProtectedRoute },
    { name: 'Advanced Search', fn: testAdvancedSearch },
    { name: 'Filter Options', fn: testFilterOptions },
    { name: 'Email Verification', fn: testEmailVerification },
    { name: 'Password Reset', fn: testPasswordReset }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log.error(`${test.name} threw an error: ${error.message}`);
      failed++;
    }
    console.log(''); // Add spacing between tests
  }
  
  console.log(`${colors.blue}📊 Test Results:${colors.reset}`);
  console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${failed}${colors.reset}`);
  console.log(`${colors.yellow}Total: ${passed + failed}${colors.reset}`);
  
  if (failed === 0) {
    console.log(`\n${colors.green}🎉 All tests passed!${colors.reset}`);
  } else {
    console.log(`\n${colors.yellow}⚠️  Some tests failed. Check the logs above.${colors.reset}`);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${BASE_URL}/api/towns`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Run tests
(async () => {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    log.error('Server is not running. Please start the development server with: npm run dev');
    process.exit(1);
  }
  
  await runTests();
})();

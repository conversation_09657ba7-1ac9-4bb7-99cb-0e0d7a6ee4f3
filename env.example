# Database
DB_USER="postgres"
DB_PASSWORD="your-db-password"
DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@localhost:5432/kenyamatch"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here"

# JWT
JWT_SECRET="your-jwt-secret-key-here"

# Admin Credentials
ADMIN_EMAIL="<EMAIL>" # Set your admin email
ADMIN_PASSWORD="your-admin-password" # Set your admin password

# Demo User Password (for seed data)
DEMO_USER_PASSWORD="demo12345" # Used for all demo/sample users

# Stripe (for payments)
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME="your_cloudinary_cloud_name"
CLOUDINARY_API_KEY="your_cloudinary_api_key"
CLOUDINARY_API_SECRET="your_cloudinary_api_secret"

# M-Pesa (for Kenyan payments)
MPESA_CONSUMER_KEY="your_mpesa_consumer_key"
MPESA_CONSUMER_SECRET="your_mpesa_consumer_secret"
MPESA_PASSKEY="your_mpesa_passkey"
MPESA_BUSINESS_SHORTCODE="your_mpesa_business_shortcode"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
SUBSCRIPTION_PRICE=1200 # Price in Kenyan Shillings
SUBSCRIPTION_DURATION_DAYS=120 
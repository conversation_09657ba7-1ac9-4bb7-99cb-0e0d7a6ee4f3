#!/bin/bash

# Create .env file with admin configuration
cat > .env << EOF
# Database
DATABASE_URL="postgresql://[1m$DB_USER[0m:[1m$DB_PASSWORD[0m@localhost:5432/kenyamatch"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="$NEXTAUTH_SECRET"

# JWT
JWT_SECRET="$JWT_SECRET"

# Admin Configuration
ADMIN_EMAIL="$ADMIN_EMAIL"
ADMIN_PASSWORD="$ADMIN_PASSWORD"

# Stripe (for payments)
STRIPE_PUBLISHABLE_KEY="$STRIPE_PUBLISHABLE_KEY"
STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY"
STRIPE_WEBHOOK_SECRET="$STRIPE_WEBHOOK_SECRET"

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME="$CLOUDINARY_CLOUD_NAME"
CLOUDINARY_API_KEY="$CLOUDINARY_API_KEY"
CLOUDINARY_API_SECRET="$CLOUDINARY_API_SECRET"

# M-Pesa (for Kenyan payments)
MPESA_CONSUMER_KEY="$MPESA_CONSUMER_KEY"
MPESA_CONSUMER_SECRET="$MPESA_CONSUMER_SECRET"
MPESA_PASSKEY="$MPESA_PASSKEY"
MPESA_BUSINESS_SHORTCODE="$MPESA_BUSINESS_SHORTCODE"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
SUBSCRIPTION_PRICE=1200 # Price in Kenyan Shillings
SUBSCRIPTION_DURATION_DAYS=120
EOF

echo "✅ Environment file created with admin credentials:"
echo "   Email: <EMAIL>"
echo "   Password: jack75522r"
echo ""
echo "📝 Please update the API keys for Stripe, Cloudinary, and M-Pesa as needed." 